import pytest
from unittest.mock import patch, MagicMock, call
import sys
import os
import smtplib
import psycopg2
import cx_Oracle
from psycopg2 import sql as sql_module # Import for testing sql composition
import subprocess
from datetime import datetime

# Assuming pytest is run from the root f:\Geoscripts directory
# or that the path is otherwise configured (e.g., PYTHONPATH)
try:
    from geoserver.postgis_cache_mngr import (
        create_parser,
        send_mail,
        connect_db,
        close_db,
        get_dim,
        print_log,
        execute_pg,
        fetchone_pg,
        fetchall_ora,
        fetchone_ora,
        execute_ora,
        get_geom_db,
        get_ora_count,
        get_pg_count,
        write_db_log,
        post_update,
        read_db,
        setup_logging
    )
except ImportError:
    # Fallback if running directly from tests dir or path not set
    current_dir = os.path.dirname(os.path.abspath(__file__))
    geoserver_dir = os.path.dirname(current_dir)
    geoscripts_dir = os.path.dirname(geoserver_dir)
    if geoscripts_dir not in sys.path:
        sys.path.insert(0, geoscripts_dir)
    from geoserver.postgis_cache_mngr import (
        create_parser,
        send_mail,
        connect_db,
        close_db,
        get_dim,
        print_log,
        execute_pg,
        fetchone_pg,
        fetchall_ora,
        fetchone_ora,
        execute_ora,
        get_geom_db,
        get_ora_count,
        get_pg_count,
        write_db_log,
        post_update,
        read_db,
        setup_logging
    )

# --- Test Argument Parsing ---

@pytest.fixture
def parser():
    """Provides a parser instance for tests."""
    return create_parser()

def test_argparse_sched_prod(parser):
    """Tests if -sp argument is parsed correctly."""
    args = parser.parse_args(['--sched-prod']) # Use long form for clarity in test
    assert args.schedule_type == 'P'
    assert args.identifier is None
    assert not args.update_flag

def test_argparse_sched_test_short(parser):
    """Tests if -st argument is parsed correctly."""
    args = parser.parse_args(['--sched-test']) # Use long form for clarity in test
    assert args.schedule_type == 'T'
    assert args.identifier is None
    assert not args.update_flag

def test_argparse_id_map(parser):
    """Tests if --id for map IDs is parsed correctly."""
    args = parser.parse_args(['--id', '123,456'])
    assert args.schedule_type is None
    assert args.identifier == '123,456'
    assert not args.update_flag

def test_argparse_id_layer(parser):
    """Tests if --id for layer IDs is parsed correctly."""
    args = parser.parse_args(['--id', 'L789'])
    assert args.schedule_type is None
    assert args.identifier == 'L789'
    assert not args.update_flag

def test_argparse_id_map_with_flag(parser):
    """Tests if --id for map IDs and --update-flag are parsed correctly."""
    args = parser.parse_args(['--id', '123', '--update-flag'])
    assert args.schedule_type is None
    assert args.identifier == '123'
    assert args.update_flag

def test_argparse_id_layer_with_flag(parser):
    """Tests if --id for layer IDs and --update-flag are parsed correctly (flag should be stored but ignored later)."""
    args = parser.parse_args(['--id', 'L999', '--update-flag'])
    assert args.schedule_type is None
    assert args.identifier == 'L999'
    assert args.update_flag # Parser stores it, logic in main() ignores it

def test_argparse_missing_required_arg(parser):
    """Tests error handling when no required argument is provided."""
    with pytest.raises(SystemExit):
        parser.parse_args([])

def test_argparse_mutually_exclusive_args(parser):
    """Tests error handling when mutually exclusive arguments are provided."""
    with pytest.raises(SystemExit):
        parser.parse_args(['--sched-prod', '--id', '123'])

    with pytest.raises(SystemExit):
        parser.parse_args(['--sched-test', '--id', 'L456'])

    with pytest.raises(SystemExit):
        parser.parse_args(['--sched-prod', '--sched-test'])

# --- Test send_mail --- MOCKING smtplib ---

@patch('geoserver.postgis_cache_mngr.smtplib.SMTP')
@patch('geoserver.postgis_cache_mngr.print_log') # Mock logging as well
def test_send_mail_success(mock_print_log, mock_smtp_class):
    """Tests successful email sending."""
    # Arrange
    mock_smtp_instance = MagicMock()
    mock_smtp_class.return_value.__enter__.return_value = mock_smtp_instance

    to_addresses = "<EMAIL>; <EMAIL>"
    subject = "Test Subject"
    body = "Test Body"
    from_address = "<EMAIL>"
    smtp_server = "smtp.test.com"

    # Patch environment variables used by send_mail
    with patch.dict(os.environ, {
        "SMTP_MAIL_SERVER": smtp_server,
        "FROM_ADDRESS": from_address
    }):
        # Act
        send_mail(to_addresses, subject, body)

    # Assert
    mock_smtp_class.assert_called_once_with(smtp_server, 25)
    mock_smtp_instance.sendmail.assert_called_once()
    # Check arguments passed to sendmail
    args, kwargs = mock_smtp_instance.sendmail.call_args
    assert args[0] == from_address
    assert sorted(args[1]) == sorted(['<EMAIL>', '<EMAIL>'])
    assert f"Subject: {subject}" in args[2]
    assert f"From: {from_address}" in args[2]
    assert f"To: <EMAIL>, <EMAIL>" in args[2]
    assert body in args[2]
    # Check logs
    mock_print_log.assert_any_call("INFO", f"Connecting to SMTP server: {smtp_server}:25")
    mock_print_log.assert_any_call("INFO", "Email sent successfully.")

@patch('geoserver.postgis_cache_mngr.smtplib.SMTP')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_send_mail_smtp_error(mock_print_log, mock_smtp_class):
    """Tests error handling during SMTP connection/send."""
    # Arrange
    mock_smtp_instance = MagicMock()
    mock_smtp_instance.sendmail.side_effect = smtplib.SMTPRecipientsRefused({'<EMAIL>': (550, 'User unknown')})
    mock_smtp_class.return_value.__enter__.return_value = mock_smtp_instance

    # Patch environment variables
    with patch.dict(os.environ, {
        "SMTP_MAIL_SERVER": "smtp.test.com",
        "FROM_ADDRESS": "<EMAIL>"
    }):
        # Act
        send_mail("<EMAIL>", "Error Test", "Body")

    # Assert
    mock_print_log.assert_any_call("ERROR", "SMTP server refused recipients: <EMAIL>")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_send_mail_no_server(mock_print_log):
    """Tests behavior when SMTP_MAIL_SERVER is not set."""
    # Arrange
    # Ensure SMTP_MAIL_SERVER is not in the environment for this test
    with patch.dict(os.environ, {}, clear=True): # Start with empty env
         # Set other required vars if send_mail checks them before server
         os.environ["FROM_ADDRESS"] = "<EMAIL>"
         # Act
         send_mail("<EMAIL>", "No Server Test", "Body")

    # Assert
    mock_print_log.assert_any_call("ERROR", "SMTP server not configured. Cannot send email.")

# --- Test Database Connections --- MOCKING DB drivers ---

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.psycopg2.connect')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_connect_db_success(mock_print_log, mock_pg_connect, mock_ora_connect):
    """Tests successful connection to both databases."""
    # Arrange
    mock_pg_conn = MagicMock()
    mock_ora_conn = MagicMock()
    mock_pg_connect.return_value = mock_pg_conn
    mock_ora_connect.return_value = mock_ora_conn

    # Patch environment variables used by connect_db
    with patch.dict(os.environ, {
        "PG_HOST": "pg.test", "PG_PORT": "5432", "PG_DB_NAME": "pgdb", "PG_USER": "pgu", "PG_PWD": "pgp",
        "ORA_DB_CONN_STR": "ora.test:1521/orasvc", "ORA_USER": "orau", "ORA_PWD": "orap"
    }):
        # Act
        # Need to access the global connections set by connect_db
        # This requires patching the globals within the module under test
        with patch('geoserver.postgis_cache_mngr.pg_conn') as mock_global_pg,
             patch('geoserver.postgis_cache_mngr.ora_conn') as mock_global_ora:

            connect_db()

            # Assert connections were made
            mock_pg_connect.assert_called_once_with(host='pg.test', port='5432', dbname='pgdb', user='pgu', password='pgp')
            mock_ora_connect.assert_called_once_with(user='orau', password='orap', dsn='ora.test:1521/orasvc')

            # Assert globals were set (this part is tricky, depends on how globals are used)
            # A better approach might be for connect_db to RETURN the connections
            # For now, we assume it sets globals correctly if no exception
            assert mock_global_pg is not None # Check if the patched global was assigned
            assert mock_global_ora is not None

            # Assert logging
            mock_print_log.assert_any_call("INFO", "Connecting to PostgreSQL: pg.test:5432/pgdb")
            mock_print_log.assert_any_call("INFO", "PostgreSQL connection successful.")
            mock_print_log.assert_any_call("INFO", "Connecting to Oracle: ora.test:1521/orasvc")
            mock_print_log.assert_any_call("INFO", "Oracle connection successful.")

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.psycopg2.connect')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_connect_db_pg_fails(mock_print_log, mock_pg_connect, mock_ora_connect):
    """Tests failure during PostgreSQL connection."""
    # Arrange
    mock_pg_connect.side_effect = psycopg2.OperationalError("PG connection failed")

    with patch.dict(os.environ, {
        "PG_HOST": "pg.test", "PG_PORT": "5432", "PG_DB_NAME": "pgdb", "PG_USER": "pgu", "PG_PWD": "pgp",
        "ORA_DB_CONN_STR": "ora.test:1521/orasvc", "ORA_USER": "orau", "ORA_PWD": "orap"
    }):
        # Act & Assert
        with pytest.raises(psycopg2.OperationalError):
            connect_db()

        # Assert Oracle connect was not called
        mock_ora_connect.assert_not_called()
        # Assert logging
        mock_print_log.assert_any_call("ERROR", "Failed to connect to PostgreSQL: PG connection failed")

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.psycopg2.connect')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_connect_db_ora_fails(mock_print_log, mock_pg_connect, mock_ora_connect):
    """Tests failure during Oracle connection."""
    # Arrange
    mock_pg_conn = MagicMock()
    mock_pg_connect.return_value = mock_pg_conn
    mock_ora_connect.side_effect = cx_Oracle.DatabaseError("ORA connection failed")

    with patch.dict(os.environ, {
        "PG_HOST": "pg.test", "PG_PORT": "5432", "PG_DB_NAME": "pgdb", "PG_USER": "pgu", "PG_PWD": "pgp",
        "ORA_DB_CONN_STR": "ora.test:1521/orasvc", "ORA_USER": "orau", "ORA_PWD": "orap"
    }):
        # Act & Assert
        with pytest.raises(cx_Oracle.DatabaseError):
             # Patch globals again if connect_db modifies them before raising
             with patch('geoserver.postgis_cache_mngr.pg_conn'),\
                  patch('geoserver.postgis_cache_mngr.ora_conn'):
                connect_db()

        # Assert PG connect was called, Ora connect was called, PG close was called
        mock_pg_connect.assert_called_once()
        mock_ora_connect.assert_called_once()
        mock_pg_conn.close.assert_called_once() # Check cleanup
        # Assert logging
        mock_print_log.assert_any_call("ERROR", "Failed to connect to Oracle: ORA connection failed")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_close_db(mock_print_log):
    """Tests closing of database connections."""
    # Arrange
    mock_pg = MagicMock()
    mock_ora = MagicMock()

    # Patch the global connection variables
    with patch('geoserver.postgis_cache_mngr.pg_conn', mock_pg),
         patch('geoserver.postgis_cache_mngr.ora_conn', mock_ora):

        # Act
        close_db()

        # Assert
        mock_pg.close.assert_called_once()
        mock_ora.close.assert_called_once()
        mock_print_log.assert_any_call("INFO", "PostgreSQL connection closed.")
        mock_print_log.assert_any_call("INFO", "Oracle connection closed.")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_close_db_one_conn_none(mock_print_log):
    """Tests closing when one connection is None."""
    # Arrange
    mock_pg = MagicMock()

    # Patch the global connection variables (ora_conn is None)
    with patch('geoserver.postgis_cache_mngr.pg_conn', mock_pg),
         patch('geoserver.postgis_cache_mngr.ora_conn', None):

        # Act
        close_db()

        # Assert
        mock_pg.close.assert_called_once()
        # No error should occur for ora_conn being None
        mock_print_log.assert_any_call("INFO", "PostgreSQL connection closed.")
        # Ensure Oracle close log message wasn't printed
        assert call("INFO", "Oracle connection closed.") not in mock_print_log.call_args_list

# --- Test get_dim --- MOCKING ORA DB Calls ---

@pytest.fixture
def mock_geom_db_cursor():
    """Provides a mock cursor for geometry DB operations."""
    mock_cursor = MagicMock()
    mock_conn = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    return mock_conn, mock_cursor

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_dim_success(mock_print_log, mock_geom_db_cursor):
    """Tests successful dimension retrieval."""
    # Arrange
    mock_conn, mock_cursor = mock_geom_db_cursor
    # Simulate fetchone returning a dimension (e.g., '2' for polygon/line)
    mock_cursor.fetchone.return_value = ('2',)
    feature_name = 'MY_TABLE'
    geom_field = 'GEOMETRY'

    # Act
    dim = get_dim(mock_conn, feature_name, geom_field)

    # Assert
    assert dim == '2'
    # Check the SQL executed (case-insensitive table/column names might matter)
    expected_sql = f"SELECT SUBSTR(t.{geom_field}.SDO_GTYPE, 1, 1) FROM {feature_name} t WHERE ROWNUM = 1"
    mock_cursor.execute.assert_called_once_with(expected_sql)
    mock_print_log.assert_any_call("INFO", f"getDim found: 2")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_dim_finds_geom_field(mock_print_log, mock_geom_db_cursor):
    """Tests finding the geometry field if not provided."""
    # Arrange
    mock_conn, mock_cursor = mock_geom_db_cursor
    # Simulate finding geom field first, then finding dimension
    mock_cursor.fetchone.side_effect = [('FOUND_GEOM_COL',), ('3',)]
    feature_name = 'ANOTHER_TABLE'
    geom_field_param = None # Geom field not provided

    # Act
    dim = get_dim(mock_conn, feature_name, geom_field_param)

    # Assert
    assert dim == '3'
    # Check calls to cursor
    assert mock_cursor.execute.call_count == 2
    # Call 1: Find geom column
    meta_sql = "SELECT COLUMN_NAME FROM USER_SDO_GEOM_METADATA WHERE TABLE_NAME = :table_name"
    mock_cursor.execute.assert_any_call(meta_sql, {'table_name': feature_name.upper()})
    # Call 2: Get dimension using the found column name
    dim_sql = f"SELECT SUBSTR(t.FOUND_GEOM_COL.SDO_GTYPE, 1, 1) FROM {feature_name} t WHERE ROWNUM = 1"
    mock_cursor.execute.assert_any_call(dim_sql)
    mock_print_log.assert_any_call("INFO", f"getDim GEOM_FIELD found: FOUND_GEOM_COL")
    mock_print_log.assert_any_call("INFO", f"getDim found: 3")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_dim_geom_field_not_found(mock_print_log, mock_geom_db_cursor):
    """Tests case where geometry field cannot be found in metadata."""
    # Arrange
    mock_conn, mock_cursor = mock_geom_db_cursor
    # Simulate finding no geom field
    mock_cursor.fetchone.return_value = None
    feature_name = 'TABLE_NO_META'
    geom_field_param = None

    # Act
    dim = get_dim(mock_conn, feature_name, geom_field_param)

    # Assert
    assert dim is None
    meta_sql = "SELECT COLUMN_NAME FROM USER_SDO_GEOM_METADATA WHERE TABLE_NAME = :table_name"
    mock_cursor.execute.assert_called_once_with(meta_sql, {'table_name': feature_name.upper()})
    mock_print_log.assert_any_call("WARNING", f"Could not find geometry column for {feature_name} in USER_SDO_GEOM_METADATA")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_dim_db_error(mock_print_log, mock_geom_db_cursor):
    """Tests handling of database error during dimension query."""
    # Arrange
    mock_conn, mock_cursor = mock_geom_db_cursor
    mock_cursor.execute.side_effect = cx_Oracle.DatabaseError("ORA-12345: some error")
    feature_name = 'ERROR_TABLE'
    geom_field = 'GEOM'

    # Act
    dim = get_dim(mock_conn, feature_name, geom_field)

    # Assert
    assert dim is None
    mock_print_log.assert_any_call("ERROR", f"Error getting dimension for {feature_name}: ORA-12345: some error")

# --- Test Database Helper Functions --- MOCKING DB Cursors ---

@pytest.fixture
def mock_pg_cursor():
    """Provides a mock PostgreSQL cursor."""
    mock_cursor = MagicMock()
    mock_conn = MagicMock()
    # Mock the context manager use (`with conn.cursor() as cur:`)
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    # Patch the global pg_conn used by the helper functions
    with patch('geoserver.postgis_cache_mngr.pg_conn', mock_conn):
        yield mock_conn, mock_cursor # Yield mocks for use in tests

@pytest.fixture
def mock_ora_cursor():
    """Provides a mock Oracle cursor."""
    mock_cursor = MagicMock()
    mock_conn = MagicMock()
    mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
    # Patch the global ora_conn used by the helper functions
    with patch('geoserver.postgis_cache_mngr.ora_conn', mock_conn):
        yield mock_conn, mock_cursor

@patch('geoserver.postgis_cache_mngr.print_log') # Mock logging if helpers use it
def test_execute_pg(mock_print_log, mock_pg_cursor):
    """Tests execute_pg function."""
    mock_conn, mock_cursor = mock_pg_cursor
    sql = "UPDATE my_table SET col = %s WHERE id = %s"
    params = ('value', 1)
    execute_pg(sql, params)
    mock_cursor.execute.assert_called_once_with(sql, params)
    # Check autocommit (default True in script)
    assert mock_conn.autocommit is True

@patch('geoserver.postgis_cache_mngr.print_log')
def test_fetchone_pg(mock_print_log, mock_pg_cursor):
    """Tests fetchone_pg function."""
    mock_conn, mock_cursor = mock_pg_cursor
    expected_result = ('result1',)
    mock_cursor.fetchone.return_value = expected_result
    sql = "SELECT col1 FROM my_table WHERE id = %s"
    params = (1,)
    result = fetchone_pg(sql, params)
    mock_cursor.execute.assert_called_once_with(sql, params)
    assert result == expected_result

@patch('geoserver.postgis_cache_mngr.print_log')
def test_fetchall_ora(mock_print_log, mock_ora_cursor):
    """Tests fetchall_ora function."""
    mock_conn, mock_cursor = mock_ora_cursor
    expected_result = [('row1', 1), ('row2', 2)]
    mock_cursor.fetchall.return_value = expected_result
    sql = "SELECT col1, col2 FROM ora_table WHERE type = :typ"
    params = {'typ': 'A'}
    result = fetchall_ora(sql, params)
    mock_cursor.execute.assert_called_once_with(sql, params)
    assert result == expected_result

@patch('geoserver.postgis_cache_mngr.print_log')
def test_fetchone_ora(mock_print_log, mock_ora_cursor):
    """Tests fetchone_ora function."""
    mock_conn, mock_cursor = mock_ora_cursor
    expected_result = ('single_result',)
    mock_cursor.fetchone.return_value = expected_result
    sql = "SELECT name FROM ora_table WHERE id = :id"
    params = {'id': 10}
    result = fetchone_ora(sql, params)
    mock_cursor.execute.assert_called_once_with(sql, params)
    assert result == expected_result

@patch('geoserver.postgis_cache_mngr.print_log')
def test_execute_ora(mock_print_log, mock_ora_cursor):
    """Tests execute_ora function."""
    mock_conn, mock_cursor = mock_ora_cursor
    sql = "INSERT INTO log_table (msg) VALUES (:msg)"
    params = {'msg': 'hello'}
    execute_ora(sql, params)
    mock_cursor.execute.assert_called_once_with(sql, params)
    mock_conn.commit.assert_called_once() # Check commit was called

# --- Test get_geom_db --- MOCKING ORA Connect/Fetch ---

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.fetchone_ora') # Mock the helper function
@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_geom_db_finds_conn_str(mock_print_log, mock_fetchone_ora, mock_cx_connect):
    """Tests get_geom_db when connection string is found via alias."""
    # Arrange
    stringa_conn = "testuser/testpwd@DB_ALIAS"
    found_dsn = "found_host:1521/found_service"
    mock_fetchone_ora.return_value = (found_dsn,) # Simulate finding the DSN
    mock_geom_conn = MagicMock()
    mock_cx_connect.return_value = mock_geom_conn

    # Act
    result_conn = get_geom_db(stringa_conn)

    # Assert
    mock_fetchone_ora.assert_called_once_with(
        "select CONNECTION_STRING from SIT_DB_ISTANZE where UPPER(ALIAS_NAME) = UPPER(:alias)",
        {'alias': 'DB_ALIAS'}
    )
    mock_cx_connect.assert_called_once_with(user='testuser', password='testpwd', dsn=found_dsn)
    assert result_conn == mock_geom_conn
    mock_print_log.assert_any_call("INFO", f"Connecting to Geometry Oracle DB: {found_dsn} as testuser")

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.fetchone_ora')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_geom_db_conn_str_not_found(mock_print_log, mock_fetchone_ora, mock_cx_connect):
    """Tests get_geom_db when alias doesn't match in SIT_DB_ISTANZE."""
    # Arrange
    stringa_conn = "user/pwd@UNKNOWN_ALIAS"
    default_dsn = 'amb_db.regione.liguria.it:1522/SIT' # Default from script
    mock_fetchone_ora.return_value = None # Simulate not finding the DSN
    mock_geom_conn = MagicMock()
    mock_cx_connect.return_value = mock_geom_conn

    # Act
    result_conn = get_geom_db(stringa_conn)

    # Assert
    mock_fetchone_ora.assert_called_once_with(
        "select CONNECTION_STRING from SIT_DB_ISTANZE where UPPER(ALIAS_NAME) = UPPER(:alias)",
        {'alias': 'UNKNOWN_ALIAS'}
    )
    # Should connect with default DSN and parsed user/pwd
    mock_cx_connect.assert_called_once_with(user='user', password='pwd', dsn=default_dsn)
    assert result_conn == mock_geom_conn
    mock_print_log.assert_any_call("INFO", f"Connecting to Geometry Oracle DB: {default_dsn} as user")

@patch('geoserver.postgis_cache_mngr.cx_Oracle.connect')
@patch('geoserver.postgis_cache_mngr.fetchone_ora')
@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_geom_db_connect_fails(mock_print_log, mock_fetchone_ora, mock_cx_connect):
    """Tests get_geom_db when the final connection fails."""
    # Arrange
    stringa_conn = "user/pwd@ALIAS"
    found_dsn = "found_host:1521/found_service"
    mock_fetchone_ora.return_value = (found_dsn,)
    mock_cx_connect.side_effect = cx_Oracle.DatabaseError("Connection failed")

    # Act & Assert
    with pytest.raises(cx_Oracle.DatabaseError):
        get_geom_db(stringa_conn)

    mock_print_log.assert_any_call("ERROR", f"Failed to connect to Geometry Oracle DB ({found_dsn}): Connection failed")

# --- Test Counting Functions --- MOCKING DB Cursors ---

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_ora_count(mock_print_log, mock_ora_cursor):
    """Tests get_ora_count."""
    mock_conn, mock_cursor = mock_ora_cursor
    mock_cursor.fetchone.return_value = (123,)
    feature_name = 'ORA_TABLE_COUNT'

    count = get_ora_count(mock_conn, feature_name)

    assert count == '123'
    mock_cursor.execute.assert_called_once_with(f"SELECT COUNT(*) FROM {feature_name}")

@patch('geoserver.postgis_cache_mngr.print_log')
def test_get_pg_count(mock_print_log, mock_pg_cursor):
    """Tests get_pg_count."""
    # We mock fetchone_pg directly as it uses the global pg_conn
    with patch('geoserver.postgis_cache_mngr.fetchone_pg') as mock_fetch:
        mock_fetch.return_value = (456,)
        feature_name_pg = 'public.pg_table_count'

        count = get_pg_count(feature_name_pg)

        assert count == '456'
        mock_fetch.assert_called_once_with(f"SELECT COUNT(*) FROM {feature_name_pg}")

# --- Test write_db_log --- MOCKING execute_ora ---

@patch('geoserver.postgis_cache_mngr.execute_ora') # Mock the helper
@patch('geoserver.postgis_cache_mngr.print_log')
def test_write_db_log(mock_print_log, mock_exec_ora):
    """Tests write_db_log function."""
    conn_str = 'user/pwd@ALIAS'
    tab_geom = 'TABLE_NAME'
    time_min = 15
    instance = 'T1'
    expected_sql = """
            INSERT INTO LOG_CACHE_PG (LOG_DATE, CONNECTION_STRING, TABLE_NAME, EXEC_TIME_MIN, INSTANCE)
            VALUES (CURRENT_TIMESTAMP, :conn_str, :tab_geom, :exec_time, :instance)
        """
    expected_params = {
        'conn_str': conn_str,
        'tab_geom': tab_geom,
        'exec_time': time_min,
        'instance': instance
    }

    write_db_log(conn_str, tab_geom, time_min, instance)

    mock_exec_ora.assert_called_once_with(expected_sql, expected_params)
    mock_print_log.assert_any_call("INFO", f"Logged execution time for {tab_geom}: {time_min} mins")

# --- Test post_update --- MOCKING execute_pg ---

@patch('geoserver.postgis_cache_mngr.execute_pg') # Mock the helper
@patch('geoserver.postgis_cache_mngr.print_log')
def test_post_update(mock_print_log, mock_exec_pg):
    """Tests post_update function executes commands."""
    post_update()

    # Check that execute_pg was called multiple times (at least 2 for the views)
    assert mock_exec_pg.call_count >= 2
    # Can check specific SQL if needed, but brittle. Check logs instead.
    mock_print_log.assert_any_call("INFO", "POST UPDATE: Executing specific view updates")
    mock_print_log.assert_any_call("INFO", "Esecuzione Post-Update Comando 1")
    mock_print_log.assert_any_call("INFO", "Post-Update Comando 1 OK")
    mock_print_log.assert_any_call("INFO", "Esecuzione Post-Update Comando 2")
    mock_print_log.assert_any_call("INFO", "Post-Update Comando 2 OK")
    mock_print_log.assert_any_call("INFO", "Post-Update finished. 2/2 commands executed successfully.")

@patch('geoserver.postgis_cache_mngr.execute_pg') # Mock the helper
@patch('geoserver.postgis_cache_mngr.print_log')
def test_post_update_error(mock_print_log, mock_exec_pg):
    """Tests post_update function handles errors."""
    # Simulate error on the second command
    mock_exec_pg.side_effect = [None, psycopg2.Error("View creation failed")]

    post_update()

    assert mock_exec_pg.call_count == 2
    mock_print_log.assert_any_call("INFO", "Post-Update Comando 1 OK")
    mock_print_log.assert_any_call("ERROR", "Errore durante Post-Update Comando 2: View creation failed")
    mock_print_log.assert_any_call("INFO", "Post-Update finished. 1/2 commands executed successfully.")


# --- Test import_table --- MOCKING Dependencies ---

@pytest.fixture
def mock_import_dependencies(mocker):
    """Mocks all dependencies for import_table."""
    mocks = {
        'get_geom_db': mocker.patch('geoserver.postgis_cache_mngr.get_geom_db'),
        'get_dim': mocker.patch('geoserver.postgis_cache_mngr.get_dim'),
        'subprocess_run': mocker.patch('subprocess.run'),
        'execute_pg': mocker.patch('geoserver.postgis_cache_mngr.execute_pg'),
        'get_ora_count': mocker.patch('geoserver.postgis_cache_mngr.get_ora_count'),
        'get_pg_count': mocker.patch('geoserver.postgis_cache_mngr.get_pg_count'),
        'print_log': mocker.patch('geoserver.postgis_cache_mngr.print_log'),
        'sql_identifier': mocker.patch('geoserver.postgis_cache_mngr.sql_module.Identifier'),
        'sql_sql': mocker.patch('geoserver.postgis_cache_mngr.sql_module.SQL'),
        'pg_conn': mocker.patch('geoserver.postgis_cache_mngr.pg_conn') # Mock global pg_conn
    }

    # Mock the SQL composition objects to return predictable strings/objects
    mocks['sql_identifier'].side_effect = lambda x: f'"{x}"' # Simple quoting for testing
    mocks['sql_sql'].return_value.format.return_value.as_string.return_value = "mocked_sql_string"
    mocks['sql_sql'].return_value.as_string.return_value = "mocked_sql_string"

    # Mock get_geom_db to return a mock connection with a close method
    mock_geom_conn = MagicMock()
    mocks['get_geom_db'].return_value = mock_geom_conn

    # Mock subprocess.run to return a successful result by default
    mock_proc_result = MagicMock(spec=subprocess.CompletedProcess)
    mock_proc_result.returncode = 0
    mock_proc_result.stdout = "OGR Success"
    mock_proc_result.stderr = ""
    mocks['subprocess_run'].return_value = mock_proc_result

    # Mock get_dim to return a valid dimension
    mocks['get_dim'].return_value = '2'

    # Mock counts
    mocks['get_ora_count'].return_value = '100'
    mocks['get_pg_count'].return_value = '100'

    # Mock pg_conn attributes/methods used in import_table (VACUUM part)
    mocks['pg_conn'].autocommit = True # Initial state
    mock_pg_cursor = MagicMock()
    mocks['pg_conn'].cursor.return_value.__enter__.return_value = mock_pg_cursor

    return mocks, mock_geom_conn

def test_import_table_success(mock_import_dependencies):
    """Tests a successful run of import_table."""
    mocks, mock_geom_conn = mock_import_dependencies

    # Arrange
    stringa_conn = 'user/pwd@ALIAS'
    tab_geom = 'MY_ORA_TABLE'
    pg_schema = 'public'
    epsg_code = '3003'
    pk_field = 'ID_PK'
    geom_field = 'GEOM'
    ora_schema = 'ORAUSER'

    # Act
    import_table(stringa_conn, tab_geom, pg_schema, epsg_code, pk_field, geom_field, ora_schema)

    # Assert
    mocks['get_geom_db'].assert_called_once_with(stringa_conn)
    mocks['get_dim'].assert_called_once_with(mock_geom_conn, tab_geom, geom_field)
    mocks['subprocess_run'].assert_called_once() # Check OGR was called
    # Check some key OGR args (can be more specific)
    ogr_args, ogr_kwargs = mocks['subprocess_run'].call_args
    assert f'OCI:{stringa_conn}:{tab_geom}' in ogr_args[0]
    assert f'-nln {pg_schema}.{ora_schema.lower()}_{tab_geom.lower()}' in ogr_args[0]
    assert f'-a_srs EPSG:{epsg_code}' in ogr_args[0]
    assert '-lco DIM=2' in ogr_args[0]

    # Check validation, index, vacuum calls (execute_pg)
    assert mocks['execute_pg'].call_count >= 2 # Validation + Index
    # Check VACUUM was attempted (via pg_conn cursor)
    assert mocks['pg_conn'].cursor.call_count >= 1 # For VACUUM
    mocks['pg_conn'].commit.assert_called_once() # Commit after VACUUM

    mocks['get_ora_count'].assert_called_once_with(mock_geom_conn, tab_geom)
    mocks['get_pg_count'].assert_called_once_with(f'{pg_schema}.{ora_schema.lower()}_{tab_geom.lower()}')
    mocks['print_log'].assert_any_call("INFO", "IMPORT OK")
    mock_geom_conn.close.assert_called_once() # Ensure geom db connection closed

def test_import_table_get_geom_db_fails(mock_import_dependencies):
    """Tests import_table when get_geom_db returns None."""
    mocks, mock_geom_conn = mock_import_dependencies
    mocks['get_geom_db'].return_value = None

    with pytest.raises(Exception, match="Failed to get geometry DB connection"):
        import_table('user/pwd@ALIAS', 'TABLE', 'public', '3003', 'ID', 'GEOM', 'ORAUSER')

    mocks['get_dim'].assert_not_called()
    mocks['subprocess_run'].assert_not_called()


def test_import_table_get_dim_fails(mock_import_dependencies):
    """Tests import_table when get_dim returns None."""
    mocks, mock_geom_conn = mock_import_dependencies
    mocks['get_dim'].return_value = None

    with pytest.raises(Exception, match="Dimension determination failed for FAILED_DIM_TABLE"):
        import_table('user/pwd@ALIAS', 'FAILED_DIM_TABLE', 'public', '3003', 'ID', 'GEOM', 'ORAUSER')

    mocks['subprocess_run'].assert_not_called()
    mock_geom_conn.close.assert_called_once() # Should still close connection

def test_import_table_ogr_fails(mock_import_dependencies):
    """Tests import_table when the OGR subprocess fails."""
    mocks, mock_geom_conn = mock_import_dependencies
    mock_proc_result = MagicMock(spec=subprocess.CompletedProcess)
    mock_proc_result.returncode = 1
    mock_proc_result.stdout = "OGR Output"
    mock_proc_result.stderr = "OGR ERROR: Something went wrong"
    mocks['subprocess_run'].return_value = mock_proc_result

    with pytest.raises(IOError, match="ERRORE ELABORAZIONE OGR: OGR ERROR: Something went wrong"):
        import_table('user/pwd@ALIAS', 'OGR_FAIL_TABLE', 'public', '3003', 'ID', 'GEOM', 'ORAUSER')

    mocks['execute_pg'].assert_not_called() # Should not proceed to PG steps
    mock_geom_conn.close.assert_called_once()

def test_import_table_ogr_timeout(mock_import_dependencies):
    """Tests import_table when the OGR subprocess times out."""
    mocks, mock_geom_conn = mock_import_dependencies
    mocks['subprocess_run'].side_effect = subprocess.TimeoutExpired(cmd='ogr2ogr...', timeout=10)

    with pytest.raises(TimeoutError, match="ERRORE ELABORAZIONE TIMEOUT"):
        import_table('user/pwd@ALIAS', 'OGR_TIMEOUT_TABLE', 'public', '3003', 'ID', 'GEOM', 'ORAUSER')

    mocks['execute_pg'].assert_not_called()
    mock_geom_conn.close.assert_called_once()

def test_import_table_counts_mismatch(mock_import_dependencies):
    """Tests import_table logs warning when counts mismatch."""
    mocks, mock_geom_conn = mock_import_dependencies
    mocks['get_ora_count'].return_value = '100'
    mocks['get_pg_count'].return_value = '99' # Mismatch

    import_table('user/pwd@ALIAS', 'COUNT_MISMATCH', 'public', '3003', 'ID', 'GEOM', 'ORAUSER')

    mocks['print_log'].assert_any_call("WARNING", "Record counts do not match!")
    mock_geom_conn.close.assert_called_once()


# --- Test read_db --- MOCKING ORA Cursor and called functions ---

@pytest.fixture
def mock_read_db_dependencies(mocker):
    """Mocks dependencies for read_db."""
    mocks = {
        'ora_conn': mocker.patch('geoserver.postgis_cache_mngr.ora_conn'),
        'import_table': mocker.patch('geoserver.postgis_cache_mngr.import_table'),
        'write_db_log': mocker.patch('geoserver.postgis_cache_mngr.write_db_log'),
        'send_mail': mocker.patch('geoserver.postgis_cache_mngr.send_mail'),
        'print_log': mocker.patch('geoserver.postgis_cache_mngr.print_log'),
        'datetime_now': mocker.patch('geoserver.postgis_cache_mngr.datetime') # Mock datetime to control timing
    }

    # Setup mock cursor for ora_conn
    mock_cursor = MagicMock()
    mocks['ora_conn'].cursor.return_value.__enter__.return_value = mock_cursor

    # Setup mock datetime
    mocks['datetime_now'].now.return_value = datetime(2024, 1, 1, 12, 0, 0)
    # Mock timedelta calculation if needed (subtracting datetimes)
    # Example: mock start and end times to control duration
    start_time = datetime(2024, 1, 1, 12, 0, 0)
    end_time = datetime(2024, 1, 1, 12, 5, 0) # 5 minutes later
    mocks['datetime_now'].now.side_effect = [start_time, end_time, start_time, end_time] # Example for two loops

    return mocks, mock_cursor

def test_read_db_success(mock_read_db_dependencies):
    """Tests read_db processing multiple rows successfully."""
    mocks, mock_cursor = mock_read_db_dependencies
    main_logger_mock = MagicMock()

    # Arrange: Simulate cursor returning two rows
    row1 = {'STRINGA_CONN': 'uc1/pc1@ac1', 'TAB_GEOM': 'tab1', 'EPSG_CODE': '3003', 'PK_FIELD': 'id1', 'GEOM_FIELD': 'geom1', 'ORA_SCHEMA': 'SCHEMA1'}
    row2 = {'STRINGA_CONN': 'uc2/pc2@ac2', 'TAB_GEOM': 'tab2', 'EPSG_CODE': '3004', 'PK_FIELD': None, 'GEOM_FIELD': None, 'ORA_SCHEMA': 'SCHEMA2'}
    mock_cursor.__iter__.return_value = [row1, row2]
    # Set cursor description for row factory
    mock_cursor.description = [('STRINGA_CONN',), ('TAB_GEOM',), ('EPSG_CODE',), ('PK_FIELD',), ('GEOM_FIELD',), ('ORA_SCHEMA',)]

    comando = "SELECT ... FROM PG_CACHE_LAYERS WHERE ..."
    params = {'p_instance': 'T'}

    # Act
    read_db(comando, main_logger_mock, params)

    # Assert
    mock_cursor.execute.assert_called_once_with(comando, params)
    assert mocks['import_table'].call_count == 2
    mocks['import_table'].assert_any_call('uc1/pc1@ac1', 'tab1', 'public', '3003', 'id1', 'geom1', 'SCHEMA1')
    mocks['import_table'].assert_any_call('uc2/pc2@ac2', 'tab2', 'public', '3004', None, None, 'SCHEMA2')

    assert mocks['write_db_log'].call_count == 2
    # Check time calculation (5 minutes = 300 seconds / 60 = 5)
    mocks['write_db_log'].assert_any_call('uc1/pc1@ac1', 'tab1', 5, '') # Instance is global, assume empty for test
    mocks['write_db_log'].assert_any_call('uc2/pc2@ac2', 'tab2', 5, '')

    mocks['send_mail'].assert_not_called()
    mocks['print_log'].assert_any_call("INFO", "Finished processing tables. Success: 2, Failed: 0")

def test_read_db_import_fails(mock_read_db_dependencies):
    """Tests read_db when import_table raises an exception."""
    mocks, mock_cursor = mock_read_db_dependencies
    main_logger_mock = MagicMock()

    # Arrange: Simulate one row, import_table fails
    row1 = {'STRINGA_CONN': 'uc1/pc1@ac1', 'TAB_GEOM': 'fail_tab', 'EPSG_CODE': '3003', 'PK_FIELD': 'id1', 'GEOM_FIELD': 'geom1', 'ORA_SCHEMA': 'SCHEMA1'}
    mock_cursor.__iter__.return_value = [row1]
    mock_cursor.description = [('STRINGA_CONN',), ('TAB_GEOM',), ('EPSG_CODE',), ('PK_FIELD',), ('GEOM_FIELD',), ('ORA_SCHEMA',)]

    import_error = IOError("OGR Failed")
    mocks['import_table'].side_effect = import_error

    comando = "SELECT ..."
    params = {}

    # Patch global ERROR_TO_ADDRESS used by send_mail call inside read_db
    with patch.dict(os.environ, {"ERROR_TO_ADDRESS": "<EMAIL>"}):
        # Act
        read_db(comando, main_logger_mock, params)

    # Assert
    mock_cursor.execute.assert_called_once_with(comando, params)
    mocks['import_table'].assert_called_once_with('uc1/pc1@ac1', 'fail_tab', 'public', '3003', 'id1', 'geom1', 'SCHEMA1')
    mocks['write_db_log'].assert_not_called() # Should fail before logging success

    # Check error logging and email
    mocks['print_log'].assert_any_call("ERROR", f"Failed to process table fail_tab from connection uc1/pc1@ac1. Error: {import_error}")
    mocks['send_mail'].assert_called_once()
    args, kwargs = mocks['send_mail'].call_args
    assert args[0] == "<EMAIL>"
    assert args[1] == "Errore Cache POSTGIS - Tabella Specifica"
    assert "CONNESSIONE: uc1/pc1@ac1" in args[2]
    assert "TAVOLA: fail_tab" in args[2]
    assert f"ERRORE: {import_error}" in args[2]

    mocks['print_log'].assert_any_call("INFO", "Finished processing tables. Success: 0, Failed: 1")
    mocks['print_log'].assert_any_call("WARNING", "1 tables failed to import.")

def test_read_db_skips_missing_data(mock_read_db_dependencies):
    """Tests read_db skips rows with essential missing data."""
    mocks, mock_cursor = mock_read_db_dependencies
    main_logger_mock = MagicMock()

    # Arrange: Simulate one row with missing TAB_GEOM
    row1 = {'STRINGA_CONN': 'uc1/pc1@ac1', 'TAB_GEOM': None, 'EPSG_CODE': '3003', 'PK_FIELD': 'id1', 'GEOM_FIELD': 'geom1', 'ORA_SCHEMA': 'SCHEMA1'}
    mock_cursor.__iter__.return_value = [row1]
    mock_cursor.description = [('STRINGA_CONN',), ('TAB_GEOM',), ('EPSG_CODE',), ('PK_FIELD',), ('GEOM_FIELD',), ('ORA_SCHEMA',)]

    comando = "SELECT ..."
    params = {}

    # Act
    read_db(comando, main_logger_mock, params)

    # Assert
    mock_cursor.execute.assert_called_once_with(comando, params)
    mocks['import_table'].assert_not_called()
    mocks['write_db_log'].assert_not_called()
    mocks['send_mail'].assert_not_called()
    mocks['print_log'].assert_any_call("WARNING", f"Skipping row due to missing data: {row1}")
    mocks['print_log'].assert_any_call("INFO", "Finished processing tables. Success: 0, Failed: 0")


# --- Test setup_logging --- MOCKING logging module, os, datetime ---

@pytest.fixture
def mock_logging_dependencies(mocker):
    """Mocks dependencies for setup_logging."""
    mocks = {
        'logging_basicconfig': mocker.patch('logging.basicConfig'),
        'logging_getlogger': mocker.patch('logging.getLogger'),
        'logging_filehandler': mocker.patch('logging.FileHandler'),
        'logging_streamhandler': mocker.patch('logging.StreamHandler'),
        'logging_formatter': mocker.patch('logging.Formatter'),
        'os_makedirs': mocker.patch('os.makedirs'),
        'os_path_join': mocker.patch('os.path.join', side_effect=os.path.join), # Use real join
        'datetime_now': mocker.patch('geoserver.postgis_cache_mngr.datetime'),
        'uuid4': mocker.patch('uuid.uuid4')
    }
    # Mock the logger instance returned by getLogger
    mock_main_logger = MagicMock()
    mock_main_logger.hasHandlers.return_value = False # Simulate no handlers initially
    mocks['logging_getlogger'].return_value = mock_main_logger

    # Mock datetime and uuid
    mocks['datetime_now'].now.return_value = datetime(2024, 5, 1, 10, 30, 0)
    mocks['uuid4'].return_value = 'test-uuid'

    # Mock GEOETL_HOME env var
    mocker.patch.dict(os.environ, {"GEOETL_HOME": "/mock/geoscripts"})

    return mocks, mock_main_logger

def test_setup_logging(mock_logging_dependencies):
    """Tests the setup_logging function."""
    mocks, mock_main_logger = mock_logging_dependencies
    run_uuid = 'some-run-uuid' # Passed from main

    # Act
    returned_logger = setup_logging(run_uuid)

    # Assert
    assert returned_logger == mock_main_logger

    # Check log directory creation
    expected_log_dir = os.path.join("/mock/geoscripts", '_log', 'geoserver')
    mocks['os_makedirs'].assert_called_once_with(expected_log_dir, exist_ok=True)

    # Check root logger configuration (basicConfig)
    mocks['logging_basicconfig'].assert_called_once()
    args, kwargs = mocks['logging_basicconfig'].call_args
    assert kwargs['level'] == logging.INFO
    assert 'handlers' in kwargs
    handlers = kwargs['handlers']
    assert len(handlers) == 2
    # Check FileHandler for run log
    run_log_path = os.path.join(expected_log_dir, f'postgis_cache_mngr_2024-05-01_{run_uuid}.log')
    assert isinstance(handlers[0], MagicMock) # It's the mocked FileHandler
    mocks['logging_filehandler'].assert_any_call(run_log_path)
    # Check StreamHandler
    assert isinstance(handlers[1], MagicMock) # It's the mocked StreamHandler
    mocks['logging_streamhandler'].assert_called_once_with(sys.stdout)

    # Check main logger configuration
    mocks['logging_getlogger'].assert_called_once_with('main_log')
    mock_main_logger.setLevel.assert_called_once_with(logging.INFO)
    # Check FileHandler for main log
    main_log_path = os.path.join(expected_log_dir, 'postgis-cache-mngr.log')
    mocks['logging_filehandler'].assert_any_call(main_log_path)
    # Check Formatter for main log handler
    mocks['logging_formatter'].assert_called_once_with('%(asctime)s - %(levelname)s - %(message)s', datefmt='%d-%m-%Y %H:%M:%S')
    # Check handler added to main logger
    mock_main_logger.addHandler.assert_called_once()


# --- Final Placeholder --- #
# Testing main() directly is complex due to its orchestration role.
# It's generally better to ensure all functions called by main() are well-tested.
# If main() contained significant logic itself, further refactoring might be needed
# to make that logic testable in isolation.
