import os
import sys
import uuid
import logging
import subprocess
import smtplib
import argparse # Import argparse
from email.mime.text import MIMEText
from datetime import datetime, timedelta
from dotenv import load_dotenv
import psycopg2
import oracledb

# Inizializza la modalità thick di oracledb usando la variabile d'ambiente impostata nel Dockerfile
oracledb.init_oracle_client(lib_dir=os.environ.get("ORACLE_CLIENT_LIB_DIR", "/opt/oracle/instantclient"))

# Load environment variables from .env file
load_dotenv()

# Get GEOETL_HOME after loading .env
geoscripts_home = os.getenv('GEOETL_HOME')
if not geoscripts_home:
    print("ERROR: GEOETL_HOME environment variable not set.")
    sys.exit(1)

dotenv_path = os.path.join(geoscripts_home, '.env')
load_dotenv(dotenv_path=dotenv_path)

# --- Configuration ---
# Read database credentials and connection details from environment variables
PG_USER = os.getenv("PG_USER")
PG_PWD = os.getenv("PG_PWD")
PG_HOST = os.getenv("PG_HOST")
PG_PORT = os.getenv("PG_PORT")
PG_DB_NAME = os.getenv("PG_DB_NAME")
ORA_DB_CONN_STR = os.getenv("ORA_DB_CONN_STR")
ORA_USER = os.getenv("ORA_USER")
ORA_PWD = os.getenv("ORA_PWD")
OGR2OGR_CMD = os.getenv("OGR2OGR_CMD", "ogr2ogr")  # Get OGR2OGR_CMD with default fallback

# Check if all required DB variables are loaded
required_db_vars = {
    "PG_USER": PG_USER,
    "PG_PWD": PG_PWD,
    "PG_HOST": PG_HOST,
    "PG_PORT": PG_PORT,
    "PG_DB_NAME": PG_DB_NAME,
    "ORA_DB_CONN_STR": ORA_DB_CONN_STR,
    "ORA_USER": ORA_USER,
    "ORA_PWD": ORA_PWD
}

missing_vars = [name for name, value in required_db_vars.items() if not value]
if missing_vars:
    print(f"ERROR: Missing required environment variables in .env file: {', '.join(missing_vars)}")
    sys.exit(1)

PG_URL = f"postgresql://{PG_USER}:{PG_PWD}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}"

SMTP_MAIL_SERVER = os.getenv("SMTP_MAIL_SERVER")
FROM_ADDRESS = os.getenv("FROM_ADDRESS")
ERROR_TO_ADDRESS = os.getenv("ERROR_TO_ADDRESS")
OK_TO_ADDRESS = os.getenv("OK_TO_ADDRESS")

# Check if all required email variables are loaded
required_email_vars = {
    "SMTP_MAIL_SERVER": SMTP_MAIL_SERVER,
    "FROM_ADDRESS": FROM_ADDRESS,
    "ERROR_TO_ADDRESS": ERROR_TO_ADDRESS,
    "OK_TO_ADDRESS": OK_TO_ADDRESS
}

missing_email_vars = [name for name, value in required_email_vars.items() if not value]
if missing_email_vars:
    print(f"ERROR: Missing required email environment variables in .env file: {', '.join(missing_email_vars)}")
    sys.exit(1)

LOG_DIR = os.path.join(geoscripts_home, '_log')
os.makedirs(LOG_DIR, exist_ok=True)

# --- Global Variables ---
pg_conn = None
ora_conn = None
log_file_path = None
istanza = ''
main_logger = None

# --- Logging Setup ---
def setup_logging(run_uuid):
    global log_file_path, main_logger
    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file_path = os.path.join(LOG_DIR, f'dp-ora_to_pg_{current_date}_{run_uuid}.log')
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s - %(levelname)s - %(message)s',
                        datefmt='%H:%M:%S',
                        handlers=[
                            logging.FileHandler(log_file_path),
                            logging.StreamHandler(sys.stdout) # Also print to console
                        ])

    # Configure a separate logger for the main summary log
    MAIN_LOG_FILE = os.path.join(LOG_DIR, f'dp-ora_to_pg_{current_date}.log')
    main_logger = logging.getLogger('main_log')
    main_logger.setLevel(logging.INFO)
    # Rimuovo tutti gli handler esistenti
    for handler in list(main_logger.handlers):
        main_logger.removeHandler(handler)
    main_handler = logging.FileHandler(MAIN_LOG_FILE)
    main_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%d-%m-%Y %H:%M:%S')
    main_handler.setFormatter(main_formatter)
    main_logger.addHandler(main_handler)

    return main_logger

def print_log(level, msg):
    if level.upper() == "INFO":
        logging.info(msg)
    elif level.upper() == "ERROR":
        logging.error(msg)
    elif level.upper() == "WARNING":
        logging.warning(msg)
    else:
        logging.debug(msg)

def print_log_main(level, msg, _main_logger=None):
    global main_logger
    log_entry = f"{istanza} - {msg}" if istanza else msg
    logger = main_logger if main_logger else _main_logger
    if logger:
        if level.upper() == "INFO":
            logger.info(log_entry)
        elif level.upper() == "ERROR":
            logger.error(log_entry)
        elif level.upper() == "WARNING":
            logger.warning(log_entry)
        else:
            logger.debug(log_entry)


# --- Database Functions ---
def connect_db():
    global pg_conn, ora_conn
    try:
        print_log("INFO", f"Connecting to PostgreSQL: {PG_HOST}:{PG_PORT}/{PG_DB_NAME}")
        pg_conn = psycopg2.connect(host=PG_HOST, port=PG_PORT, dbname=PG_DB_NAME, user=PG_USER, password=PG_PWD)
        pg_conn.autocommit = True # Match Groovy Sql behavior where execute doesn't need explicit commit for DDL/non-select
        print_log("INFO", "PostgreSQL connection successful.")
    except Exception as e:
        print_log("ERROR", f"Failed to connect to PostgreSQL: {e}")
        raise

    try:
        print_log("INFO", f"Connecting to Oracle: {ORA_DB_CONN_STR}")
        # Ensure Oracle Client libraries are found (might need ORACLE_HOME or PATH set)
        # oracledb.init_oracle_client(lib_dir=r"C:\path\to\instantclient") # Example if needed
        ora_conn = oracledb.connect(user=ORA_USER, password=ORA_PWD, dsn=ORA_DB_CONN_STR)
        print_log("INFO", "Oracle connection successful.")
    except Exception as e:
        print_log("ERROR", f"Failed to connect to Oracle: {e}")
        if pg_conn: pg_conn.close()
        raise

def close_db():
    if pg_conn:
        pg_conn.close()
        print_log("INFO", "PostgreSQL connection closed.")
    if ora_conn:
        ora_conn.close()
        print_log("INFO", "Oracle connection closed.")

def execute_pg(sql, params=None):
    with pg_conn.cursor() as cur:
        cur.execute(sql, params or ())
        # No fetch needed for execute, autocommit handles commit

def fetchone_pg(sql, params=None):
     with pg_conn.cursor() as cur:
        cur.execute(sql, params or ())
        return cur.fetchone()

def fetchall_ora(sql, params=None):
    with ora_conn.cursor() as cur:
        cur.execute(sql, params or {}) # Use dict for named params if needed
        # Fetch column names for dictionary cursor if desired
        # columns = [col[0] for col in cur.description]
        # cur.rowfactory = lambda *args: dict(zip(columns, args))
        return cur.fetchall()

def fetchone_ora(sql, params=None):
     with ora_conn.cursor() as cur:
        cur.execute(sql, params or {})
        # columns = [col[0] for col in cur.description]
        # cur.rowfactory = lambda *args: dict(zip(columns, args))
        return cur.fetchone()

def execute_ora(sql, params=None):
     with ora_conn.cursor() as cur:
        cur.execute(sql, params or {})
        ora_conn.commit() # Explicit commit needed for Oracle DML/DDL

def get_geom_db(stringa_conn):
    jdbc_conn_string = 'amb_db.regione.liguria.it:1522/SIT' # Default? Check Groovy logic
    usr = 'VISCARTO'
    pwd = 'CARTOVIS'
    appo_str_conn = stringa_conn.split('@')
    geom_db_conn = None

    try:
        if len(appo_str_conn) >= 2:
            alias = appo_str_conn[1]
            # Use main Oracle connection to query SIT_DB_ISTANZE
            conn_str_row = fetchone_ora("select ALIAS_NAME from SIT_DB_ISTANZE where UPPER(ALIAS_NAME) = UPPER(:alias)", {'alias': alias})
            if conn_str_row:
                jdbc_conn_string = conn_str_row[0]

            appo_str_conn2 = appo_str_conn[0].split('/')
            if len(appo_str_conn2) == 2:
                usr = appo_str_conn2[0]
                pwd = appo_str_conn2[1]
            else:
                 print_log("WARNING", f"Could not parse user/password from {appo_str_conn[0]}")

        print_log("INFO", f"Connecting to Geometry Oracle DB: {jdbc_conn_string} as {usr}")
        geom_db_conn = oracledb.connect(user=usr, password=pwd, dsn=jdbc_conn_string)
        print_log("INFO", "Geometry Oracle DB connection successful.")
        return geom_db_conn
    except Exception as e:
        print_log("ERROR", f"Failed to connect to Geometry Oracle DB ({jdbc_conn_string}): {e}")
        if geom_db_conn: geom_db_conn.close() # Ensure cleanup on partial success
        raise # Re-raise the exception

def get_dim(geom_db, feature_name, geom_field):
    dim = None
    try:
        with geom_db.cursor() as cur:
            if not geom_field:
                # Fetch geom field if not provided (assuming USER_SDO_GEOM_METADATA exists and is accessible)
                cmd_geom = "SELECT COLUMN_NAME FROM USER_SDO_GEOM_METADATA WHERE TABLE_NAME = :table_name"
                print_log("INFO", f"getDim GEOM_FIELD query: {cmd_geom} with table_name='{feature_name.upper()}'")
                cur.execute(cmd_geom, {'table_name': feature_name.upper()})
                geom_field_row = cur.fetchone()
                if geom_field_row:
                    geom_field = geom_field_row[0]
                    print_log("INFO", f"getDim GEOM_FIELD found: {geom_field}")
                else:
                    print_log("WARNING", f"Could not find geometry column for {feature_name} in USER_SDO_GEOM_METADATA")
                    return None # Cannot proceed without geometry field

            # Fetch dimension
            # Ensure geom_field is treated as an identifier, not a string literal
            # WARNING: Direct string formatting for column names can be an SQL injection risk if geom_field comes from untrusted input.
            # Here it comes from metadata or config, which is generally safer, but be aware.
            if not geom_field or not geom_field.isidentifier():
                 print_log("ERROR", f"Invalid geometry field name: {geom_field}")
                 return None

            cmd_dim = f"SELECT SUBSTR(t.{geom_field}.SDO_GTYPE, 1, 1) FROM {feature_name} t WHERE ROWNUM = 1"
            print_log("INFO", f"getDim query: {cmd_dim}")
            cur.execute(cmd_dim)
            rec = cur.fetchone()
            if rec:
                dim = rec[0]
                print_log("INFO", f"getDim found: {dim}")

    except Exception as e:
        print_log("ERROR", f"Error getting dimension for {feature_name}: {e}")
        # Potentially log the specific Oracle error code if available (e.g., e.args[0].code)

    return dim


def get_ora_count(geom_db, feature_name):
    count = "2" # Default from Groovy
    try:
        with geom_db.cursor() as cur:
            # WARNING: Direct string formatting for table names can be an SQL injection risk.
            if not feature_name or not feature_name.isidentifier():
                 print_log("ERROR", f"Invalid feature name: {feature_name}")
                 return count
            cur.execute(f"SELECT COUNT(*) FROM {feature_name}")
            rec = cur.fetchone()
            if rec:
                count = str(rec[0])
    except Exception as e:
        print_log("ERROR", f"Error getting Oracle count for {feature_name}: {e}")
    return count

def get_pg_count(feature_name_pg):
    count = None
    try:
        # WARNING: Direct string formatting for table names can be an SQL injection risk.
        # Assuming feature_name_pg is constructed safely internally.
        # Use schema name if necessary: f"public.{feature_name_pg}"
        # Need to handle potential SQL injection if schema/table names come from external sources.
        # For psycopg2, it's safer to use sql composition for identifiers:
        # from psycopg2 import sql
        # query = sql.SQL("SELECT COUNT(*) FROM {}.{}").format(sql.Identifier(schema_name), sql.Identifier(table_name))
        # For simplicity here, assuming internal construction is safe:
        if '.' in feature_name_pg: # Basic check for schema.table
             schema_name, table_name = feature_name_pg.split('.', 1)
             if not schema_name.isidentifier() or not table_name.isidentifier():
                  print_log("ERROR", f"Invalid PG feature name: {feature_name_pg}")
                  return None
        elif not feature_name_pg.isidentifier():
             print_log("ERROR", f"Invalid PG feature name: {feature_name_pg}")
             return None

        count_row = fetchone_pg(f"SELECT COUNT(*) FROM {feature_name_pg}")
        if count_row:
            count = str(count_row[0])
    except Exception as e:
        print_log("ERROR", f"Error getting PostgreSQL count for {feature_name_pg}: {e}")
    return count

def write_db_log(stringa_conn, tab_geom, td_tavola_minutes, istanza_log):
    try:
        # Using CURRENT_TIMESTAMP for Oracle sysdate equivalent
        # Ensure parameter binding is correct for oracledb
        
        comando = """
            INSERT INTO LOG_CACHE_PG (DATA_CACHE, STRING_CONN, TAB_GEOM, TEMPO_ELABORAZIONE_MINUTI, ISTANZA)
            VALUES (CURRENT_TIMESTAMP, :conn_str, :tab_geom, :exec_time, :instance)
        """
        params = {
            'conn_str': stringa_conn,
            'tab_geom': tab_geom,
            'exec_time': td_tavola_minutes,
            'instance': istanza_log
        }
        execute_ora(comando, params)
        print_log("INFO", f"Logged execution time for {tab_geom}: {td_tavola_minutes} mins")
    except Exception as e:
        print_log("ERROR", f"Failed to write DB log for {tab_geom}: {e}")


# --- Core Logic Functions ---
def import_table(stringa_conn, tab_geom, pg_schema, epsg_code, pk_field, geom_field, ora_schema):
    geom_db = None
    try:
        geom_db = get_geom_db(stringa_conn)
        if not geom_db:
             raise Exception("Failed to get geometry DB connection") # Or handle more gracefully

        if not tab_geom:
            print_log("WARNING", "Table name is null, skipping import.")
            return

        print_log("INFO", f"ELABORAZIONE TAVOLA: {tab_geom}")

        dim = get_dim(geom_db, tab_geom, geom_field)
        if dim is None:
             print_log("ERROR", f"Could not determine dimension for {tab_geom}, skipping import.")
             # Optionally send error mail here if skipping is critical
             raise Exception(f"Dimension determination failed for {tab_geom}") # Stop processing this table

        # Ensure ora_schema and tab_geom are valid identifiers before concatenation
        if not ora_schema.isidentifier() or not tab_geom.isidentifier():
             raise ValueError(f"Invalid Oracle schema or table name: {ora_schema}, {tab_geom}")
        pg_tab_geom = f"{ora_schema.lower()}_{tab_geom.lower()}" # Lowercase convention often used in PG

        print_log("INFO", f"DIM={dim} - PG_TABLE={pg_schema}.{pg_tab_geom}")

        # OGR Command Execution
        timeout_seconds = 70 * 60 # 70 minutes
        start_ogr = datetime.now()

        # Construct OGR command
        # Ensure paths and connection strings are quoted if they contain spaces
        pg_conn_str = f"host={PG_HOST} port={PG_PORT} dbname={PG_DB_NAME} user={PG_USER} password={PG_PWD} active_schema={pg_schema}"
        # Ensure OCI connection string is correctly formatted and potentially quoted if needed by OGR/OCI driver
        oci_conn_str = f"{stringa_conn}:{tab_geom}"
        # Ensure table/layer names are safe for shell command
        # Basic check: ensure pg_schema and pg_tab_geom are valid identifiers
        if not pg_schema.isidentifier() or not pg_tab_geom.isidentifier():
             raise ValueError(f"Invalid PG schema or table name for OGR: {pg_schema}, {pg_tab_geom}")

        # Use list of arguments for subprocess to avoid shell injection issues
        comando_args = [
            OGR2OGR_CMD,  # Use environment variable instead of hardcoded "ogr2ogr"
            "-overwrite",
            "-f", "PostgreSQL",
            f"PG:{pg_conn_str}",
            f"OCI:{oci_conn_str}",
            "-sql", f"SELECT * FROM {tab_geom}", # Table name from Oracle side
            "-a_srs", f"EPSG:{epsg_code}",
            "--config", "PG_USE_COPY", "YES",
            "-lco", "FID=ogr_fid", # Or keep original PK if preferred and possible
            "-lco", f"DIM={dim}",
            "-nln", f"{pg_schema}.{pg_tab_geom}" # Target table name in PG
        ]
        comando_ogr = " ".join(comando_args) # Join for logging
        comando_ogr = comando_ogr.replace(pg_conn_str, "<PG_CONN_STR>").replace(oci_conn_str,"<OCI_CONN_STR>") # Replace single quotes with double quotes for logging
        print_log("INFO", f"COMANDO OGR: {comando_ogr}") # Log the command string for readability

        try:
            # Use subprocess.run for simpler blocking execution with timeout
            proc = subprocess.run(comando_args, capture_output=True, text=True, timeout=timeout_seconds, check=False, shell=False) # shell=False is safer

            ogr_stdout = proc.stdout
            ogr_stderr = proc.stderr
            exit_code = proc.returncode

            print_log("INFO", f"OGR STDOUT:\n{ogr_stdout}")
            if ogr_stderr:
                 print_log("WARNING", f"OGR STDERR:\n{ogr_stderr}") # Log stderr even on success, might contain warnings

            # Check for specific Oracle error in stderr as in Groovy
            if "ORA-13208" in ogr_stderr:
                print_log("ERROR", "OGR failed with ORA-13208 (likely internal error during spatial query). Treating as error.")
                exit_code = 1 # Force error status

            if exit_code != 0:
                raise subprocess.CalledProcessError(exit_code, comando_args, output=ogr_stdout, stderr=ogr_stderr)

        except subprocess.TimeoutExpired:
            elapsed = datetime.now() - start_ogr
            print_log("ERROR", f"OGR command timed out after {elapsed.total_seconds():.1f} seconds (limit: {timeout_seconds}s).")
            raise TimeoutError("ERRORE ELABORAZIONE TIMEOUT")
        except subprocess.CalledProcessError as e:
            elapsed = datetime.now() - start_ogr
            print_log("ERROR", f"OGR command failed with exit code {e.returncode} after {elapsed.total_seconds():.1f} seconds.")
            print_log("ERROR", f"OGR STDERR was:\n{e.stderr}")
            raise IOError(f"ERRORE ELABORAZIONE OGR: {e.stderr[:500]}") # Limit error message length
        except Exception as e:
             print_log("ERROR", f"An unexpected error occurred during OGR execution: {e}")
             raise


        print_log("INFO", "BONIFICA GEOMETRIE NON VALIDE (Polygons)")
        # Ensure pg_tab_geom is safe here too
        # Use sql composition for safety if needed
        valid_cmd = f"""
            UPDATE {pg_schema}.{pg_tab_geom}
            SET wkb_geometry = st_multi(st_collectionextract(st_makevalid(wkb_geometry),3))
            WHERE NOT ST_IsValid(wkb_geometry)
              AND ST_GeometryType(wkb_geometry) = 'ST_Polygon'
        """
        print_log("INFO", f"COMANDO: {valid_cmd}")
        try:
            execute_pg(valid_cmd)
            print_log("INFO", "BONIFICA OK")
        except Exception as e:
            print_log("ERROR", f"Error during geometry validation: {e}")
            # Decide if this is fatal or just a warning

        print_log("INFO", "CREO INDICI E VACUUM")
        try:
            if pk_field:
                # Ensure pk_field and pg_tab_geom are valid identifiers
                if not pk_field.isidentifier(): raise ValueError(f"Invalid PK field: {pk_field}")
                # Index name needs sanitizing too, or use default naming
                index_name = f"{pg_tab_geom}_{pk_field.lower()}_pk_idx" # Example naming
                # Basic sanitization for index name
                index_name = "".join(c if c.isalnum() or c == '_' else '_' for c in index_name)[:60] # Limit length

                # Use sql composition for safety
                from psycopg2 import sql
                index_cmd = sql.SQL("""
                    CREATE UNIQUE INDEX IF NOT EXISTS {index_name}
                    ON {schema}.{table} USING btree ({pk_column})
                """).format(
                    index_name=sql.Identifier(index_name),
                    schema=sql.Identifier(pg_schema),
                    table=sql.Identifier(pg_tab_geom),
                    pk_column=sql.Identifier(pk_field.lower()) # Assuming PK field name from Oracle is case-insensitive or needs lowercasing for PG
                )
                print_log("INFO", f"Creating index: {index_cmd.as_string(pg_conn)}")
                execute_pg(index_cmd)
                print_log("INFO", f"Index on {pk_field} created (or already exists).")

        except Exception as e:
            # Catch specific psycopg2 errors if needed (e.g., DuplicateTableError for index)
            print_log("WARNING", f"Could not create PK index (maybe exists or invalid PK '{pk_field}'): {e}")
            # Reset transaction state if error occurred in non-autocommit mode
            # if not pg_conn.autocommit: pg_conn.rollback()


        print_log("INFO", "ESEGUO VACUUM/ANALYZE")
        try:
            # VACUUM deve essere eseguito fuori da una transazione esplicita
            pg_conn.autocommit = True
            from psycopg2 import sql
            vacuum_cmd = sql.SQL("VACUUM ANALYZE {schema}.{table}").format(
                schema=sql.Identifier(pg_schema),
                table=sql.Identifier(pg_tab_geom)
            )
            print_log("INFO", f"Executing: {vacuum_cmd.as_string(pg_conn)}")
            with pg_conn.cursor() as cur:
                cur.execute(vacuum_cmd)
            print_log("INFO", "VACUUM ANALYZE OK")
        except Exception as e:
            print_log("ERROR", f"Error during VACUUM ANALYZE: {e}")
            # Decide if this is fatal

        print_log("INFO", "IMPORT OK")

        # Record Counts
        ora_count = get_ora_count(geom_db, tab_geom)
        pg_count = get_pg_count(f"{pg_schema}.{pg_tab_geom}")
        print_log("INFO", "CONTEGGIO RECORD ------------------------------- ")
        print_log("INFO", f" - ORACLE ({tab_geom}): {ora_count} - PG ({pg_schema}.{pg_tab_geom}): {pg_count}")
        if ora_count != pg_count:
             print_log("WARNING", "Record counts do not match!")
             # Optionally send a specific warning email

        # Export to Parquet file
        print_log("INFO", "EXPORTING TO PARQUET")
        try:
            # Ensure the parquet directory exists
            parquet_dir = "/data/parquet"
            os.makedirs(parquet_dir, exist_ok=True)

            # Construct parquet file path using the postgres table name
            parquet_file_path = os.path.join(parquet_dir, f"{pg_tab_geom}.parquet")

            # Construct ogr2ogr command to export from PostgreSQL to Parquet
            parquet_comando_args = [
                OGR2OGR_CMD,
                "-f", "Parquet",
                parquet_file_path,
                f"PG:{pg_conn_str}",
                "-sql", f"SELECT * FROM {pg_schema}.{pg_tab_geom}"
            ]

            parquet_comando_str = " ".join(parquet_comando_args)
            # Mask connection string for logging
            parquet_comando_log = parquet_comando_str.replace(pg_conn_str, "<PG_CONN_STR>")
            print_log("INFO", f"PARQUET EXPORT COMMAND: {parquet_comando_log}")

            # Execute the parquet export command
            parquet_proc = subprocess.run(parquet_comando_args, capture_output=True, text=True, timeout=600, check=False, shell=False)

            parquet_stdout = parquet_proc.stdout
            parquet_stderr = parquet_proc.stderr
            parquet_exit_code = parquet_proc.returncode

            if parquet_stdout:
                print_log("INFO", f"PARQUET EXPORT STDOUT:\n{parquet_stdout}")
            if parquet_stderr:
                print_log("WARNING", f"PARQUET EXPORT STDERR:\n{parquet_stderr}")

            if parquet_exit_code != 0:
                raise subprocess.CalledProcessError(parquet_exit_code, parquet_comando_args, output=parquet_stdout, stderr=parquet_stderr)

            # Get file size in megabytes
            try:
                file_size_bytes = os.path.getsize(parquet_file_path)
                file_size_mb = file_size_bytes / (1024 * 1024)
                print_log("INFO", f"PARQUET EXPORT OK: {parquet_file_path} (Size: {file_size_mb:.2f} MB)")
            except OSError as e:
                print_log("WARNING", f"Could not get file size for {parquet_file_path}: {e}")
                print_log("INFO", f"PARQUET EXPORT OK: {parquet_file_path}")

        except subprocess.TimeoutExpired:
            print_log("ERROR", "Parquet export command timed out after 10 minutes.")
        except subprocess.CalledProcessError as e:
            print_log("ERROR", f"Parquet export command failed with exit code {e.returncode}.")
            print_log("ERROR", f"PARQUET EXPORT STDERR was:\n{e.stderr}")
        except Exception as e:
            print_log("ERROR", f"An unexpected error occurred during parquet export: {e}")

    except Exception as ex:
        print_log("ERROR", f"Exception during importTable for {tab_geom}: {ex}")
        # Re-raise the exception to be caught by the main loop for error reporting
        raise
    finally:
        if geom_db:
            geom_db.close()
            print_log("INFO", f"Closed Geometry Oracle DB connection")


def read_db(comando, main_logger, params=None):
    print_log("INFO", f"Esecuzione Comando Oracle: {comando} with params {params}")
    processed_tables = 0
    failed_tables = 0
    try:
        with ora_conn.cursor() as cur:
            cur.execute(comando, params or {})
            # Fetch column names to access by name (case-insensitive)
            columns = [col[0] for col in cur.description]
            cur.rowfactory = lambda *args: dict(zip(columns, args))

            for row in cur:
                # Access columns by uppercase name as in Groovy/Oracle
                stringa_conn = row.get('STRINGA_CONN')
                tab_geom = row.get('TAB_GEOM')
                epsg_code = row.get('EPSG_CODE')
                pk_field = row.get('PK_FIELD')
                geom_field = row.get('GEOM_FIELD')
                ora_schema = row.get('ORA_SCHEMA') # Already uppercase from query
                pg_schema = "public" # Default schema

                if not all([stringa_conn, tab_geom, epsg_code, ora_schema]):
                     print_log("WARNING", f"Skipping row due to missing data: {row}")
                     continue

                try:
                    start_tavola = datetime.now()
                    import_table(stringa_conn, tab_geom, pg_schema, epsg_code, pk_field, geom_field, ora_schema)
                    stop_tavola = datetime.now()
                    td_tavola = stop_tavola - start_tavola
                    td_tavola_minutes = int(td_tavola.total_seconds() / 60)
                    write_db_log(stringa_conn, tab_geom, td_tavola_minutes, istanza)
                    processed_tables += 1
                except Exception as e:
                    # Error logged within import_table or get_geom_db
                    print_log("ERROR", f"Failed to process table {tab_geom} from connection {stringa_conn}. Error: {e}")
                    failed_tables += 1
                    # Send specific error email for this table failure
                    body = f"CONNESSIONE: {stringa_conn}<br>TAVOLA: {tab_geom}<br>ERRORE: {e}"
                    send_mail(ERROR_TO_ADDRESS, "Errore Cache POSTGIS - Tabella Specifica", body)
                    # Decide whether to continue with other tables or stop
                    # continue # Uncomment to continue processing other tables despite errors

    except Exception as e:
        print_log("ERROR", f"Error executing main Oracle query or iterating results: {e}")
        raise # Raise to main block for general error reporting

    print_log("INFO", f"Finished processing tables. Success: {processed_tables}, Failed: {failed_tables}")
    if failed_tables > 0:
        # Maybe raise an exception here if any failure should stop the overall success message
        print_log("WARNING", f"{failed_tables} tables failed to import.")


def post_update():
    print_log("INFO", "POST UPDATE: Executing specific view updates")
    cmds = [
        """
            CREATE OR REPLACE VIEW public.pgr_grandi_piccole_deriv_idr
            AS SELECT genioweb_rl_s3_punti_de_view_gd.id,
                genioweb_rl_s3_punti_de_view_gd.codice_pratica,
                genioweb_rl_s3_punti_de_view_gd.denominazione,
                genioweb_rl_s3_punti_de_view_gd.wkb_geometry
            FROM public.genioweb_rl_s3_punti_de_view_gd -- Assuming schema is public
            WHERE genioweb_rl_s3_punti_de_view_gd.tipo_uso::text = 'UMANO'::text AND genioweb_rl_s3_punti_de_view_gd.descrizione_stato_prat::text = 'IN ESERCIZIO'::text
            UNION ALL
            SELECT genioweb_rl_s3_punti_de_view_pd.id,
                genioweb_rl_s3_punti_de_view_pd.codice_pratica,
                genioweb_rl_s3_punti_de_view_pd.denominazione,
                genioweb_rl_s3_punti_de_view_pd.wkb_geometry
            FROM public.genioweb_rl_s3_punti_de_view_pd -- Assuming schema is public
            WHERE genioweb_rl_s3_punti_de_view_pd.tipo_uso::text = 'UMANO'::text AND genioweb_rl_s3_punti_de_view_pd.descrizione_stato_prat::text = 'IN ESERCIZIO'::text;
        """,
        """
            CREATE OR REPLACE VIEW varie.pgr_v_sis_instab_pub -- Assuming schema varie exists
            AS SELECT v_sis_instab_pub.id,v_sis_instab_pub.id_i,v_sis_instab_pub.tipo_i,v_sis_instab_pub.decodifica,v_sis_instab_pub.cod_com,
                v_sis_instab_pub.flag_validazione,v_sis_instab_pub.frt,v_sis_instab_pub.frr,v_sis_instab_pub.il,v_sis_instab_pub.disl,
                v_sis_instab_pub.fa,v_sis_instab_pub.fv,v_sis_instab_pub.ft,v_sis_instab_pub.fh0105,v_sis_instab_pub.fh0515,v_sis_instab_pub.fpga,
                v_sis_instab_pub.fa0105,v_sis_instab_pub.fa0408,v_sis_instab_pub.fa0711,v_sis_instab_pub.spettri,v_sis_instab_pub.livello,
                v_sis_instab_pub.cat,v_sis_instab_pub.amb,v_sis_instab_pub.li,v_sis_instab_pub.valore_f,v_sis_instab_pub.tipo_f,
                v_sis_instab_pub.versione_standard,v_sis_instab_pub.wkb_geometry,v_sis_instab_pub.tipo_i_sempl
            FROM varie.v_sis_instab_pub -- Assuming schema varie exists
            WHERE v_sis_instab_pub.tipo_i not in (3060,3070)
        """
    ]

    success_count = 0
    for i, cmd in enumerate(cmds):
        print_log("INFO", f"Esecuzione Post-Update Comando {i+1}") # Log snippet removed for brevity
        try:
            execute_pg(cmd)
            print_log("INFO", f"Post-Update Comando {i+1} OK")
            success_count += 1
        except Exception as e:
            print_log("ERROR", f"Errore durante Post-Update Comando {i+1}: {e}")
            # Decide if this error is critical

    print_log("INFO", f"Post-Update finished. {success_count}/{len(cmds)} commands executed successfully.")


# --- Email Function ---
def send_mail(to_address_str, subject, body):
    if not SMTP_MAIL_SERVER:
        print_log("ERROR", "SMTP server not configured. Cannot send email.")
        return

    # Split addresses, strip whitespace
    to_addresses = [addr.strip() for addr in to_address_str.split(';') if addr.strip()]
    if not to_addresses:
        print_log("WARNING", "No valid recipient addresses provided for email.")
        return

    msg = MIMEText(body, 'html', 'utf-8') # Use utf-8, more standard than ISO-8859-1
    msg['Subject'] = subject
    msg['From'] = FROM_ADDRESS
    msg['To'] = ', '.join(to_addresses) # Comma-separated for header

    try:
        print_log("INFO", f"Connecting to SMTP server: {SMTP_MAIL_SERVER}:25")
        with smtplib.SMTP(SMTP_MAIL_SERVER, 25) as server:
            # server.set_debuglevel(1) # Uncomment for debugging SMTP
            # Add login here if authentication is required:
            # server.login(SMTP_USER, SMTP_PASSWORD)
            print_log("INFO", f"Sending email to: {', '.join(to_addresses)}")
            server.sendmail(FROM_ADDRESS, to_addresses, msg.as_string())
            print_log("INFO", "Email sent successfully.")
    except smtplib.SMTPRecipientsRefused:
         print_log("ERROR", f"SMTP server refused recipients: {', '.join(to_addresses)}")
    except smtplib.SMTPAuthenticationError:
         print_log("ERROR", "SMTP authentication failed. Check credentials if login is used.")
    except smtplib.SMTPSenderRefused:
         print_log("ERROR", f"SMTP server refused sender address: {FROM_ADDRESS}")
    except Exception as e:
        print_log("ERROR", f"Failed to send email: {e}")


def create_parser():
    """Creates and returns the ArgumentParser instance."""
    parser = argparse.ArgumentParser(description='Import Oracle spatial data to PostGIS cache based on map/layer IDs or scheduled instance.')

    # Group for mutually exclusive run types: scheduled or manual ID
    run_type_group = parser.add_mutually_exclusive_group(required=True)
    run_type_group.add_argument('-sp', '--sched-prod', action='store_const', const='P', dest='schedule_type', help='Run scheduled task for Production instance (P).')
    run_type_group.add_argument('-st', '--sched-test', action='store_const', const='T', dest='schedule_type', help='Run scheduled task for Test instance (T).')
    run_type_group.add_argument('--id', dest='identifier', help='Run for specific Map ID(s) (e.g., 123,456) or Layer ID(s) (e.g., L789,L1011).')

    parser.add_argument('--update-flag', action='store_true', help='Set POSTGIS_CACHE=\'S\' in sit_catalogo for the specified map IDs after successful import (only applicable when using --id for maps).')
    return parser

# --- Main Execution ---
def main(args_list=None):
    """Main execution logic. Accepts optional list of arguments for testing."""
    global istanza, main_logger
    # Controllo se esiste la variabile d'ambiente RUN_UUID
    env_run_uuid = os.getenv('RUN_UUID')
    run_uuid = uuid.UUID(env_run_uuid) if env_run_uuid else uuid.uuid4()
    main_logger = setup_logging(run_uuid)
    start_time = datetime.now()

    # --- Argument Parsing Setup ---
    parser = create_parser()
    # Parse actual command line args if not testing, else parse provided list
    args = parser.parse_args(args_list if args_list is not None else sys.argv[1:])
    # --- End Argument Parsing ---


    print_log("INFO", f"SCRIPT START - Run UUID: {run_uuid}")
    print_log("INFO", f"Arguments: {args}") # Log parsed arguments
    print_log_main("INFO", f"SCRIPT START - Run UUID: {run_uuid}", main_logger)

    # Determine run type and parameters
    param = None
    flag_schedulata = False
    update_postgis_flag = args.update_flag

    if args.schedule_type:
        flag_schedulata = True
        istanza = args.schedule_type # Will be 'P' or 'T'
        param = istanza # Use instance type as the parameter identifier for logging/emails
        print_log("INFO", f"Scheduled run detected for instance: {istanza}")
        if update_postgis_flag:
            print_log("WARNING", "--update-flag is ignored for scheduled runs.")
            update_postgis_flag = False # Ensure flag is not used for scheduled runs
    elif args.identifier:
        param = args.identifier
        print_log("INFO", f"Manual run detected for ID(s): {param}")
        if update_postgis_flag and param.startswith('L'):
             print_log("WARNING", "--update-flag is ignored for layer-specific runs (--id starts with L).")
             update_postgis_flag = False # Ensure flag is not used for layer runs
        elif update_postgis_flag:
             print_log("INFO", "--update-flag is set for map ID run.")
    else:
        # This case should not be reached due to the required mutually exclusive group
        print_log("ERROR", "Invalid arguments. Must specify schedule type (-sp/-st) or manual ID (--id).")
        parser.print_help()
        sys.exit(1)

    try:
        connect_db()

        print_log("INFO", "COPIA DATI POSTGIS")
        base_comando = "SELECT DISTINCT STRINGA_CONN, TAB_GEOM, UPPER(SUBSTR(STRINGA_CONN,0,INSTR(STRINGA_CONN,'/')-1)) ORA_SCHEMA, EPSG_CODE, PK_FIELD, GEOM_FIELD FROM PG_CACHE_LAYERS"
        where_clause = ""
        order_by = " ORDER BY 1, 2" # Order by STRINGA_CONN, TAB_GEOM
        params = {} # Initialize params dictionary

        if flag_schedulata:
            # Use the stored instance ('P' or 'T')
            print_log("INFO", f"Esecuzione schedulata per istanza: {istanza}")
            where_clause = " WHERE ID_LAYER IN (SELECT DISTINCT ID_LAYER FROM GS_LAYERS WHERE CACHE_SCHEDULATA LIKE :p_instance || '_%') AND POSTGIS_CACHE = 'S'"
            params = {'p_instance': istanza} # Bind the instance type ('P' or 'T')
        else:
            # Manual run using --id
            id_param = args.identifier
            print_log("INFO", f"Esecuzione singola per ID: {id_param}")
            if id_param.startswith("L"):
                # Layer ID(s)
                try:
                    layer_ids = [int(x) for x in id_param[1:].split(',') if x.isdigit()]
                    if not layer_ids: raise ValueError("No valid layer IDs found")
                    id_placeholders = ','.join([':id'+str(i) for i in range(len(layer_ids))])
                    where_clause = f" WHERE ID_LAYER IN ({id_placeholders})"
                    params = {'id'+str(i): lid for i, lid in enumerate(layer_ids)}
                except ValueError as e:
                     print_log("ERROR", f"Invalid Layer ID format (--id): {id_param}. Error: {e}")
                     sys.exit(1)
            else:
                 # Map ID(s)
                 try:
                    map_ids = [int(x) for x in id_param.split(',') if x.isdigit()]
                    if not map_ids: raise ValueError("No valid map IDs found")
                    id_placeholders = ','.join([':id'+str(i) for i in range(len(map_ids))])
                    where_clause = f" WHERE ID_MAP IN ({id_placeholders})"
                    params = {'id'+str(i): mid for i, mid in enumerate(map_ids)}
                 except ValueError as e:
                     print_log("ERROR", f"Invalid Map ID format (--id): {id_param}. Error: {e}")
                     sys.exit(1)

        # Construct and execute the command
        comando = base_comando + where_clause + order_by
        read_db(comando, main_logger, params) # Pass params dict

        # Update Oracle flag if requested (only for manual map ID runs)
        if update_postgis_flag:
             id_param = args.identifier # Already validated as map IDs if flag is true
             try:
                map_ids_to_update = [int(x) for x in id_param.split(',') if x.isdigit()]
                if map_ids_to_update:
                    update_placeholders = ','.join([':id'+str(i) for i in range(len(map_ids_to_update))])
                    update_params = {'id'+str(i): mid for i, mid in enumerate(map_ids_to_update)}
                    update_cmd = f"UPDATE sit_catalogo SET POSTGIS_CACHE = 'S' WHERE CODICE IN ({update_placeholders})"
                    execute_ora(update_cmd, update_params)
                    print_log("INFO", f"Aggiornato campo POSTGIS_CACHE in sit_catalogo per ID(s): {id_param}")
                # else case not needed due to prior validation
             except Exception as e:
                  print_log("ERROR", f"Failed to update POSTGIS_CACHE flag in sit_catalogo: {e}")

        post_update()

        stop_time = datetime.now()
        td = stop_time - start_time
        print_log("INFO", "ESECUZIONE TERMINATA")

        # Send final email
        email_param_id = istanza if flag_schedulata else args.identifier
        if flag_schedulata:
            subject = f"Esecuzione Cache Schedulata POSTGIS - {istanza}"
            body = f"CACHE POSTGIS SCHEDULATA ({istanza}): Esecuzione Terminata.<br>Tempo totale di esecuzione: {td}"
            print_log_main("INFO", f"CACHE POSTGIS SCHEDULATA: Esecuzione Terminata - {td}", main_logger)
            send_mail(OK_TO_ADDRESS, subject, body)
        else:
            subject = f"Esecuzione Cache POSTGIS - {email_param_id}"
            body = f"CACHE POSTGIS ({email_param_id}): Esecuzione Terminata.<br>Tempo totale di esecuzione: {td}"
            print_log_main("INFO", f"CACHE POSTGIS {email_param_id}: Esecuzione Terminata - {td}", main_logger)
            # send_mail(OK_TO_ADDRESS, subject, body) # Send mail for manual runs? Uncomment if needed

    except Exception as e:
        stop_time = datetime.now()
        td = stop_time - start_time
        error_param_id = istanza if flag_schedulata else args.identifier if args.identifier else "N/A"
        print_log("ERROR", f"SCRIPT FAILED ({error_param_id}): {e}")
        print_log_main("ERROR", f"SCRIPT FAILED ({error_param_id}): {e} - Runtime: {td}", main_logger)
        # Send error email for general failure
        error_body = f"Esecuzione Cache POSTGIS fallita ({error_param_id}).<br>Errore: {e}<br>Controllare il log: {log_file_path}"
        send_mail(ERROR_TO_ADDRESS, f"ERRORE Esecuzione Cache POSTGIS - {error_param_id}", error_body)
        sys.exit(1) # Exit with error code
    finally:
        close_db()
        print_log("INFO", f"SCRIPT END - Total Time: {datetime.now() - start_time}")

if __name__ == "__main__":
    main()
