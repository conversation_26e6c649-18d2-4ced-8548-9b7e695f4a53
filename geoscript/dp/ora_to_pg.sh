\
#!/bin/bash
# Launcher for postgis_cache_mngr.py on Ubuntu/Linux

# Get the directory containing this script
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

# Set GEOETL_HOME if not already set (adjust path if needed)
if [ -z "${GEOETL_HOME}" ]; then
  export GEOETL_HOME="${SCRIPT_DIR}/.."
  echo "Setting GEOETL_HOME to ${GEOETL_HOME}"
fi

# Ensure Oracle Instant Client (or full client) libs are findable if needed by cx_Oracle
# Example: export LD_LIBRARY_PATH=/path/to/instantclient_XX_Y:$LD_LIBRARY_PATH

echo "Running Python script: ${SCRIPT_DIR}/postgis_cache_mngr.py $@"
python3 "${SCRIPT_DIR}/postgis_cache_mngr.py" "$@" # Use python3 explicitly if needed

EXIT_CODE=$?
echo "Script finished. Exit code: ${EXIT_CODE}"
exit ${EXIT_CODE}
