\
@echo off
REM Launcher for postgis_cache_mngr.py on Windows
REM Assumes python is in the PATH

REM Get the directory containing this batch script
set SCRIPT_DIR=%~dp0

REM Set GEOETL_HOME if not already set (adjust path if needed)
if not defined GEOETL_HOME (
    set "GEOETL_HOME=%SCRIPT_DIR%.."
    echo Setting GEOETL_HOME to %GEOETL_HOME%
)


REM Ensure Oracle Instant Client (or full client) is in the PATH if needed by cx_Oracle
REM Example: set PATH=%PATH%;C:\path\to\instantclient_XX_Y

echo Running Python script: %SCRIPT_DIR%postgis_cache_mngr.py %*
python "%SCRIPT_DIR%postgis_cache_mngr.py" %*

echo Script finished. Exit code: %ERRORLEVEL%
pause
