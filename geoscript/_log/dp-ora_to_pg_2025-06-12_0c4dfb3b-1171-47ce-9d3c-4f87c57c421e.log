09:48:27 - INFO - SCRIPT START - Run UUID: 0c4dfb3b-1171-47ce-9d3c-4f87c57c421e
09:48:27 - INFO - Arguments: Namespace(schedule_type=None, identifier='56', update_flag=False)
09:48:27 - INFO - SCRIPT START - Run UUID: 0c4dfb3b-1171-47ce-9d3c-4f87c57c421e
09:48:27 - INFO - Manual run detected for ID(s): 56
09:48:27 - INFO - Connecting to PostgreSQL: geoservizi-db-test.regione.liguria.it:5432/viscarto
09:48:27 - INFO - PostgreSQL connection successful.
09:48:27 - INFO - Connecting to Oracle: AMB_DB_AMB
09:48:27 - INFO - Oracle connection successful.
09:48:27 - INFO - COPIA DATI POSTGIS
09:48:27 - INFO - Esecuzione singola per ID: 56
09:48:27 - INFO - Esecuzione Comando Oracle: SELECT DISTINCT STRINGA_CONN, TAB_GEOM, UPPER(SUBSTR(STRINGA_CONN,0,INSTR(STRINGA_CONN,'/')-1)) ORA_SCHEMA, EPSG_CODE, PK_FIELD, GEOM_FIELD FROM PG_CACHE_LAYERS WHERE ID_MAP IN (:id0) ORDER BY 1, 2 with params {'id0': 56}
09:48:27 - INFO - Connecting to Geometry Oracle DB: AMB_DB_SIT as cbase
09:48:27 - INFO - Geometry Oracle DB connection successful.
09:48:27 - INFO - ELABORAZIONE TAVOLA: COMUNI_5000_2015
09:48:27 - INFO - getDim query: SELECT SUBSTR(t.GDO_GEOMETRY.SDO_GTYPE, 1, 1) FROM COMUNI_5000_2015 t WHERE ROWNUM = 1
09:48:27 - INFO - getDim found: 2
09:48:27 - INFO - DIM=2 - PG_TABLE=public.cbase_comuni_5000_2015
09:48:27 - INFO - COMANDO OGR: ogr2ogr -overwrite -f PostgreSQL PG:<PG_CONN_STR> OCI:<OCI_CONN_STR> -sql SELECT * FROM COMUNI_5000_2015 -a_srs EPSG:3003 --config PG_USE_COPY YES -lco FID=ogr_fid -lco DIM=2 -nln public.cbase_comuni_5000_2015
09:48:30 - INFO - OGR STDOUT:

09:48:30 - INFO - BONIFICA GEOMETRIE NON VALIDE (Polygons)
09:48:30 - INFO - COMANDO: 
            UPDATE public.cbase_comuni_5000_2015
            SET wkb_geometry = st_multi(st_collectionextract(st_makevalid(wkb_geometry),3))
            WHERE NOT ST_IsValid(wkb_geometry)
              AND ST_GeometryType(wkb_geometry) = 'ST_Polygon'
        
09:48:30 - INFO - BONIFICA OK
09:48:30 - INFO - CREO INDICI E VACUUM
09:48:30 - INFO - Creating index: 
                    CREATE UNIQUE INDEX IF NOT EXISTS "cbase_comuni_5000_2015_id_pk_idx"
                    ON "public"."cbase_comuni_5000_2015" USING btree ("id")
                
09:48:30 - INFO - Index on ID created (or already exists).
09:48:30 - INFO - ESEGUO VACUUM/ANALYZE
09:48:30 - INFO - Executing: VACUUM ANALYZE "public"."cbase_comuni_5000_2015"
09:48:30 - INFO - VACUUM ANALYZE OK
09:48:30 - INFO - IMPORT OK
09:48:30 - INFO - CONTEGGIO RECORD ------------------------------- 
09:48:30 - INFO -  - ORACLE (COMUNI_5000_2015): 235 - PG (public.cbase_comuni_5000_2015): 235
09:48:30 - INFO - EXPORTING TO PARQUET
09:48:30 - INFO - PARQUET EXPORT COMMAND: ogr2ogr -f Parquet /data/parquet/cbase_comuni_5000_2015.parquet PG:<PG_CONN_STR> -sql SELECT * FROM public.cbase_comuni_5000_2015
09:48:31 - INFO - PARQUET EXPORT OK: /data/parquet/cbase_comuni_5000_2015.parquet (Size: 4.27 MB)
09:48:31 - INFO - Closed Geometry Oracle DB connection
09:48:31 - INFO - Logged execution time for COMUNI_5000_2015: 0 mins
09:48:31 - INFO - Finished processing tables. Success: 1, Failed: 0
09:48:31 - INFO - POST UPDATE: Executing specific view updates
09:48:31 - INFO - Esecuzione Post-Update Comando 1
09:48:31 - INFO - Post-Update Comando 1 OK
09:48:31 - INFO - Esecuzione Post-Update Comando 2
09:48:31 - INFO - Post-Update Comando 2 OK
09:48:31 - INFO - Post-Update finished. 2/2 commands executed successfully.
09:48:31 - INFO - ESECUZIONE TERMINATA
09:48:31 - INFO - CACHE POSTGIS 56: Esecuzione Terminata - 0:00:04.724727
09:48:31 - INFO - PostgreSQL connection closed.
09:48:31 - INFO - Oracle connection closed.
09:48:31 - INFO - SCRIPT END - Total Time: 0:00:04.731478
