19:25:13 - INFO - SCRIPT START - Run UUID: fc287cfb-78dd-4aa5-b7c2-12c0a6158ebf
19:25:13 - INFO - Arguments: Namespace(schedule_type=None, identifier='56', update_flag=False)
19:25:13 - INFO - SCRIPT START - Run UUID: fc287cfb-78dd-4aa5-b7c2-12c0a6158ebf
19:25:13 - INFO - Manual run detected for ID(s): 56
19:25:13 - INFO - Connecting to PostgreSQL: geoservizi-db-test.regione.liguria.it:5432/viscarto
19:25:14 - INFO - PostgreSQL connection successful.
19:25:14 - INFO - Connecting to Oracle: AMB_DB_AMB
19:25:14 - INFO - Oracle connection successful.
19:25:14 - INFO - COPIA DATI POSTGIS
19:25:14 - INFO - Esecuzione singola per ID: 56
19:25:14 - INFO - Esecuzione Comando Oracle: SELECT DISTINCT STRINGA_CONN, TAB_GEOM, UPPER(SUBSTR(STRINGA_CONN,0,INSTR(STRINGA_CONN,'/')-1)) ORA_SCHEMA, EPSG_CODE, PK_FIELD, GEOM_FIELD FROM PG_CACHE_LAYERS WHERE ID_MAP IN (:id0) ORDER BY 1, 2 with params {'id0': 56}
19:25:14 - INFO - Connecting to Geometry Oracle DB: AMB_DB_SIT as cbase
19:25:15 - INFO - Geometry Oracle DB connection successful.
19:25:15 - INFO - ELABORAZIONE TAVOLA: COMUNI_5000_2015
19:25:15 - INFO - getDim query: SELECT SUBSTR(t.GDO_GEOMETRY.SDO_GTYPE, 1, 1) FROM COMUNI_5000_2015 t WHERE ROWNUM = 1
19:25:15 - INFO - getDim found: 2
19:25:15 - INFO - DIM=2 - PG_TABLE=public.cbase_comuni_5000_2015
19:25:15 - INFO - COMANDO OGR: ogr2ogr -overwrite -f PostgreSQL PG:<PG_CONN_STR> OCI:<OCI_CONN_STR> -sql SELECT * FROM COMUNI_5000_2015 -a_srs EPSG:3003 --config PG_USE_COPY YES -lco FID=ogr_fid -lco DIM=2 -nln public.cbase_comuni_5000_2015
19:25:32 - INFO - OGR STDOUT:

19:25:32 - INFO - BONIFICA GEOMETRIE NON VALIDE (Polygons)
19:25:32 - INFO - COMANDO: 
            UPDATE public.cbase_comuni_5000_2015
            SET wkb_geometry = st_multi(st_collectionextract(st_makevalid(wkb_geometry),3))
            WHERE NOT ST_IsValid(wkb_geometry)
              AND ST_GeometryType(wkb_geometry) = 'ST_Polygon'
        
19:25:32 - INFO - BONIFICA OK
19:25:32 - INFO - CREO INDICI E VACUUM
19:25:32 - INFO - Creating index: 
                    CREATE UNIQUE INDEX IF NOT EXISTS "cbase_comuni_5000_2015_id_pk_idx"
                    ON "public"."cbase_comuni_5000_2015" USING btree ("id")
                
19:25:32 - INFO - Index on ID created (or already exists).
19:25:32 - INFO - ESEGUO VACUUM/ANALYZE
19:25:32 - INFO - Executing: VACUUM ANALYZE "public"."cbase_comuni_5000_2015"
19:25:32 - INFO - VACUUM ANALYZE OK
19:25:32 - INFO - IMPORT OK
19:25:32 - INFO - CONTEGGIO RECORD ------------------------------- 
19:25:32 - INFO -  - ORACLE (COMUNI_5000_2015): 235 - PG (public.cbase_comuni_5000_2015): 235
19:25:32 - INFO - Closed Geometry Oracle DB connection
19:25:32 - INFO - Logged execution time for COMUNI_5000_2015: 0 mins
19:25:32 - INFO - Finished processing tables. Success: 1, Failed: 0
19:25:32 - INFO - POST UPDATE: Executing specific view updates
19:25:32 - INFO - Esecuzione Post-Update Comando 1
19:25:32 - INFO - Post-Update Comando 1 OK
19:25:32 - INFO - Esecuzione Post-Update Comando 2
19:25:32 - INFO - Post-Update Comando 2 OK
19:25:32 - INFO - Post-Update finished. 2/2 commands executed successfully.
19:25:32 - INFO - ESECUZIONE TERMINATA
19:25:32 - INFO - CACHE POSTGIS 56: Esecuzione Terminata - 0:00:18.949400
19:25:32 - INFO - PostgreSQL connection closed.
19:25:32 - INFO - Oracle connection closed.
19:25:32 - INFO - SCRIPT END - Total Time: 0:00:19.004958
