14:19:41 - INFO - SCRIPT START - Run UUID: 83e6b69b-a8e1-43d8-b4c3-85167460c5a0
14:19:41 - INFO - Arguments: Namespace(schedule_type=None, identifier='56', update_flag=False)
14:19:41 - INFO - SCRIPT START - Run UUID: 83e6b69b-a8e1-43d8-b4c3-85167460c5a0
14:19:41 - INFO - Manual run detected for ID(s): 56
14:19:41 - INFO - Connecting to PostgreSQL: geoservizi-db-test.regione.liguria.it:5432/viscarto
14:19:41 - INFO - PostgreSQL connection successful.
14:19:41 - INFO - Connecting to Oracle: AMB_DB_AMB
14:19:42 - INFO - Oracle connection successful.
14:19:42 - INFO - COPIA DATI POSTGIS
14:19:42 - INFO - Esecuzione singola per ID: 56
14:19:42 - INFO - Esecuzione Comando Oracle: SELECT DISTINCT STRINGA_CONN, TAB_GEOM, UPPER(SUBSTR(STRINGA_CONN,0,INSTR(STRINGA_CONN,'/')-1)) ORA_SCHEMA, EPSG_CODE, PK_FIELD, GEOM_FIELD FROM PG_CACHE_LAYERS WHERE ID_MAP IN (:id0) ORDER BY 1, 2 with params {'id0': 56}
14:19:42 - INFO - Connecting to Geometry Oracle DB: AMB_DB_SIT as cbase
14:19:42 - INFO - Geometry Oracle DB connection successful.
14:19:42 - INFO - ELABORAZIONE TAVOLA: COMUNI_5000_2015
14:19:42 - INFO - getDim query: SELECT SUBSTR(t.GDO_GEOMETRY.SDO_GTYPE, 1, 1) FROM COMUNI_5000_2015 t WHERE ROWNUM = 1
14:19:42 - INFO - getDim found: 2
14:19:42 - INFO - DIM=2 - PG_TABLE=public.cbase_comuni_5000_2015
14:19:42 - INFO - COMANDO OGR: ogr2ogr -overwrite -f PostgreSQL PG:<PG_CONN_STR> OCI:<OCI_CONN_STR> -sql SELECT * FROM COMUNI_5000_2015 -a_srs EPSG:3003 --config PG_USE_COPY YES -lco FID=ogr_fid -lco DIM=2 -nln public.cbase_comuni_5000_2015
14:19:57 - INFO - OGR STDOUT:

14:19:57 - INFO - BONIFICA GEOMETRIE NON VALIDE (Polygons)
14:19:57 - INFO - COMANDO: 
            UPDATE public.cbase_comuni_5000_2015
            SET wkb_geometry = st_multi(st_collectionextract(st_makevalid(wkb_geometry),3))
            WHERE NOT ST_IsValid(wkb_geometry)
              AND ST_GeometryType(wkb_geometry) = 'ST_Polygon'
        
14:19:57 - INFO - BONIFICA OK
14:19:57 - INFO - CREO INDICI E VACUUM
14:19:57 - INFO - Creating index: 
                    CREATE UNIQUE INDEX IF NOT EXISTS "cbase_comuni_5000_2015_id_pk_idx"
                    ON "public"."cbase_comuni_5000_2015" USING btree ("id")
                
14:19:57 - INFO - Index on ID created (or already exists).
14:19:57 - INFO - ESEGUO VACUUM/ANALYZE
14:19:57 - INFO - Executing: VACUUM ANALYZE "public"."cbase_comuni_5000_2015"
14:19:57 - INFO - VACUUM ANALYZE OK
14:19:57 - INFO - IMPORT OK
14:19:57 - INFO - CONTEGGIO RECORD ------------------------------- 
14:19:57 - INFO -  - ORACLE (COMUNI_5000_2015): 235 - PG (public.cbase_comuni_5000_2015): 235
14:19:57 - INFO - Closed Geometry Oracle DB connection
14:19:58 - INFO - Logged execution time for COMUNI_5000_2015: 0 mins
14:19:58 - INFO - Finished processing tables. Success: 1, Failed: 0
14:19:58 - INFO - POST UPDATE: Executing specific view updates
14:19:58 - INFO - Esecuzione Post-Update Comando 1
14:19:58 - INFO - Post-Update Comando 1 OK
14:19:58 - INFO - Esecuzione Post-Update Comando 2
14:19:58 - INFO - Post-Update Comando 2 OK
14:19:58 - INFO - Post-Update finished. 2/2 commands executed successfully.
14:19:58 - INFO - ESECUZIONE TERMINATA
14:19:58 - INFO - CACHE POSTGIS 56: Esecuzione Terminata - 0:00:17.004826
14:19:58 - INFO - PostgreSQL connection closed.
14:19:58 - INFO - Oracle connection closed.
14:19:58 - INFO - SCRIPT END - Total Time: 0:00:17.062066
