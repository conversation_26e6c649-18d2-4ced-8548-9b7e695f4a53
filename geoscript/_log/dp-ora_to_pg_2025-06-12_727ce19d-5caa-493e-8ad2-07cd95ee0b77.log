09:42:56 - INFO - SCRIPT START - Run UUID: 727ce19d-5caa-493e-8ad2-07cd95ee0b77
09:42:56 - INFO - Arguments: Namespace(schedule_type=None, identifier='56', update_flag=False)
09:42:56 - INFO - SCRIPT START - Run UUID: 727ce19d-5caa-493e-8ad2-07cd95ee0b77
09:42:56 - INFO - Manual run detected for ID(s): 56
09:42:56 - INFO - Connecting to PostgreSQL: geoservizi-db-test.regione.liguria.it:5432/viscarto
09:42:57 - INFO - PostgreSQL connection successful.
09:42:57 - INFO - Connecting to Oracle: AMB_DB_AMB
09:42:57 - INFO - Oracle connection successful.
09:42:57 - INFO - COPIA DATI POSTGIS
09:42:57 - INFO - Esecuzione singola per ID: 56
09:42:57 - INFO - Esecuzione Comando Oracle: SELECT DISTINCT STRINGA_CONN, TAB_GEOM, UPPER(SUBSTR(STRINGA_CONN,0,INSTR(STRINGA_CONN,'/')-1)) ORA_SCHEMA, EPSG_CODE, PK_FIELD, GEOM_FIELD FROM PG_CACHE_LAYERS WHERE ID_MAP IN (:id0) ORDER BY 1, 2 with params {'id0': 56}
09:42:57 - INFO - Connecting to Geometry Oracle DB: AMB_DB_SIT as cbase
09:42:57 - INFO - Geometry Oracle DB connection successful.
09:42:57 - INFO - ELABORAZIONE TAVOLA: COMUNI_5000_2015
09:42:57 - INFO - getDim query: SELECT SUBSTR(t.GDO_GEOMETRY.SDO_GTYPE, 1, 1) FROM COMUNI_5000_2015 t WHERE ROWNUM = 1
09:42:57 - INFO - getDim found: 2
09:42:57 - INFO - DIM=2 - PG_TABLE=public.cbase_comuni_5000_2015
09:42:57 - INFO - COMANDO OGR: ogr2ogr -overwrite -f PostgreSQL PG:<PG_CONN_STR> OCI:<OCI_CONN_STR> -sql SELECT * FROM COMUNI_5000_2015 -a_srs EPSG:3003 --config PG_USE_COPY YES -lco FID=ogr_fid -lco DIM=2 -nln public.cbase_comuni_5000_2015
09:43:04 - INFO - OGR STDOUT:

09:43:04 - INFO - BONIFICA GEOMETRIE NON VALIDE (Polygons)
09:43:04 - INFO - COMANDO: 
            UPDATE public.cbase_comuni_5000_2015
            SET wkb_geometry = st_multi(st_collectionextract(st_makevalid(wkb_geometry),3))
            WHERE NOT ST_IsValid(wkb_geometry)
              AND ST_GeometryType(wkb_geometry) = 'ST_Polygon'
        
09:43:04 - INFO - BONIFICA OK
09:43:04 - INFO - CREO INDICI E VACUUM
09:43:04 - INFO - Creating index: 
                    CREATE UNIQUE INDEX IF NOT EXISTS "cbase_comuni_5000_2015_id_pk_idx"
                    ON "public"."cbase_comuni_5000_2015" USING btree ("id")
                
09:43:04 - INFO - Index on ID created (or already exists).
09:43:04 - INFO - ESEGUO VACUUM/ANALYZE
09:43:04 - INFO - Executing: VACUUM ANALYZE "public"."cbase_comuni_5000_2015"
09:43:04 - INFO - VACUUM ANALYZE OK
09:43:04 - INFO - IMPORT OK
09:43:04 - INFO - CONTEGGIO RECORD ------------------------------- 
09:43:04 - INFO -  - ORACLE (COMUNI_5000_2015): 235 - PG (public.cbase_comuni_5000_2015): 235
09:43:04 - INFO - EXPORTING TO PARQUET
09:43:04 - INFO - PARQUET EXPORT COMMAND: ogr2ogr -f Parquet /data/parquet/cbase_comuni_5000_2015.parquet PG:<PG_CONN_STR> -sql SELECT * FROM public.cbase_comuni_5000_2015
09:43:05 - INFO - PARQUET EXPORT OK: /data/parquet/cbase_comuni_5000_2015.parquet
09:43:05 - INFO - Closed Geometry Oracle DB connection
09:43:05 - INFO - Logged execution time for COMUNI_5000_2015: 0 mins
09:43:05 - INFO - Finished processing tables. Success: 1, Failed: 0
09:43:05 - INFO - POST UPDATE: Executing specific view updates
09:43:05 - INFO - Esecuzione Post-Update Comando 1
09:43:05 - INFO - Post-Update Comando 1 OK
09:43:05 - INFO - Esecuzione Post-Update Comando 2
09:43:05 - INFO - Post-Update Comando 2 OK
09:43:05 - INFO - Post-Update finished. 2/2 commands executed successfully.
09:43:05 - INFO - ESECUZIONE TERMINATA
09:43:05 - INFO - CACHE POSTGIS 56: Esecuzione Terminata - 0:00:08.879427
09:43:05 - INFO - PostgreSQL connection closed.
09:43:05 - INFO - Oracle connection closed.
09:43:05 - INFO - SCRIPT END - Total Time: 0:00:08.890677
