from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException, Query
from typing import List, Optional
from pydantic import BaseModel, EmailStr
from dotenv import load_dotenv
import smtplib
from email.message import EmailMessage
import os
import subprocess
import shlex

# Per<PERSON><PERSON> base degli script all'interno del container
BASE_SCRIPT_PATH = "/srv/geoscript"
# Percorso dello script 'run' per attivare l'ambiente virtuale
RUN_SCRIPT_EXECUTABLE = os.path.join(BASE_SCRIPT_PATH, "run")

# Carica variabili d'ambiente da .env
load_dotenv(os.path.join(BASE_SCRIPT_PATH, '.env'))

# Configurazione email
SMTP_MAIL_SERVER = os.getenv('SMTP_MAIL_SERVER')
FROM_ADDRESS = os.getenv('FROM_ADDRESS')
ERROR_TO_ADDRESS = os.getenv('ERROR_TO_ADDRESS')
OK_TO_ADDRESS = os.getenv('OK_TO_ADDRESS')

app = FastAPI()

class ScriptExecutionRequest(BaseModel):
    args: Optional[List[str]] = None
    email: Optional[EmailStr] = None

def send_notification_email(to_address: Optional[str], is_error: bool, subject: str, body: str):
    """
    Invia email di notifica usando l'indirizzo fornito o quello predefinito da .env
    """
    if not SMTP_MAIL_SERVER or not FROM_ADDRESS:
        print("Configurazione SMTP o FROM_ADDRESS mancante, impossibile inviare email.")
        return

    # Se non è specificato un indirizzo, usa quello predefinito in base al tipo di notifica
    final_to_address = to_address or (ERROR_TO_ADDRESS if is_error else OK_TO_ADDRESS)
    if not final_to_address:
        print("Nessun indirizzo email specificato e nessun indirizzo predefinito configurato.")
        return

    try:
        msg = EmailMessage()
        msg['Subject'] = subject
        msg['From'] = FROM_ADDRESS
        msg['To'] = final_to_address
        msg.set_content(body)

        with smtplib.SMTP(SMTP_MAIL_SERVER) as server:
            server.send_message(msg)
        print(f"Email inviata a {final_to_address}")
    except Exception as e:
        print(f"Errore invio email: {e}")

def run_script_in_background(script_relative_path: str, args: Optional[List[str]] = None, email: Optional[str] = None):
    """
    Funzione eseguita in background per lanciare uno script Python
    utilizzando lo script 'run' per gestire l'ambiente.
    """
    full_script_path = os.path.join(BASE_SCRIPT_PATH, script_relative_path)

    if not os.path.exists(full_script_path):
        error_msg = f"Script non trovato in {full_script_path}"
        print(f"Errore BackgroundTask: {error_msg}")
        send_notification_email(
            email,
            is_error=True,
            subject=f"Script {script_relative_path} non trovato",
            body=error_msg
        )
        return

    try:
        command_parts = [RUN_SCRIPT_EXECUTABLE, script_relative_path]
        if args:
            command_parts.extend(args)

        print(f"Esecuzione comando in background: {command_parts}")

        process = subprocess.Popen(
            command_parts,
            shell=False,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=BASE_SCRIPT_PATH
        )
        stdout, stderr = process.communicate()

        stdout_text = stdout.decode() if stdout else ""
        stderr_text = stderr.decode() if stderr else ""

        if process.returncode == 0:
            print(f"Script {script_relative_path} completato con successo.")
            if stdout_text:
                print(f"Output:\n{stdout_text}")

            send_notification_email(
                email,
                is_error=False,
                subject=f"Script {script_relative_path} completato con successo",
                body=f"Lo script è stato eseguito con successo.\n\nOutput:\n{stdout_text}"
            )
        else:
            error_msg = f"Errore durante l'esecuzione. Codice: {process.returncode}\n\n"
            if stderr_text:
                error_msg += f"Stderr:\n{stderr_text}\n"
            if stdout_text:
                error_msg += f"Stdout:\n{stdout_text}"

            print(f"Errore esecuzione {script_relative_path}: {error_msg}")

            send_notification_email(
                email,
                is_error=True,
                subject=f"Errore esecuzione script {script_relative_path}",
                body=error_msg
            )

    except Exception as e:
        error_msg = f"Errore imprevisto: {str(e)}"
        print(f"Errore nell'esecuzione della background task per {script_relative_path}: {error_msg}")

        send_notification_email(
            email,
            is_error=True,
            subject=f"Errore imprevisto script {script_relative_path}",
            body=error_msg
        )

@app.post("/run-script-async/{script_name}")
async def trigger_script(
    script_name: str,
    background_tasks: BackgroundTasks,
    request_body: Optional[ScriptExecutionRequest] = None
):
    """
    Endpoint per avviare l'esecuzione di uno script in background.
    Accetta argomenti opzionali e indirizzo email nel corpo della richiesta JSON:
    {
        "args": ["--param1", "value1"],
        "email": "<EMAIL>"
    }
    Se email non è specificato, usa OK_TO_ADDRESS/ERROR_TO_ADDRESS da .env
    """
    scripts_available = {
        "ora_to_pg": "dp/ora_to_pg.py",
        "load_postgis": "rqa/load_postgis.py"
    }

    script_relative_path = scripts_available.get(script_name)
    if not script_relative_path:
        raise HTTPException(status_code=404, detail="Script non valido o non trovato")

    script_args = request_body.args if request_body and request_body.args is not None else []
    email = request_body.email if request_body else None

    background_tasks.add_task(run_script_in_background, script_relative_path, script_args, email)

    return {
        "message": f"Esecuzione dello script '{script_name}' avviata in background.",
        "args": script_args,
        "notification_email": email or f"{OK_TO_ADDRESS} (default success) / {ERROR_TO_ADDRESS} (default error)"
    }

@app.post("/run-script-sync/{script_name}")
async def trigger_script_sync(
    script_name: str,
    request_body: Optional[ScriptExecutionRequest] = None
):
    """
    Endpoint per avviare l'esecuzione SINCRONA di uno script.
    Attende il completamento dello script e restituisce l'output/errore.
    """
    # Mappa il nome ricevuto a un percorso di script RELATIVO a BASE_SCRIPT_PATH
    scripts_available = {
        "ora_to_pg": "dp/ora_to_pg.py",
        "load_postgis": "rqa/load_postgis.py"
        # Aggiungi altri script qui
    }

    script_relative_path = scripts_available.get(script_name)

    if not script_relative_path:
        raise HTTPException(status_code=404, detail="Script non valido o non trovato")

    full_script_path = os.path.join(BASE_SCRIPT_PATH, script_relative_path)

    # Verifica esistenza dello script target
    if not os.path.exists(full_script_path):
         raise HTTPException(status_code=404, detail=f"Script file non trovato in {full_script_path}")

    try:
        # Costruisce il comando usando lo script 'run'
        command_parts = [RUN_SCRIPT_EXECUTABLE, script_relative_path]
        # Qui potresti voler accettare argomenti dalla richiesta e aggiungerli a command_parts
        # command_parts.extend(args_from_request)

        command = shlex.join(command_parts)

        print(f"Esecuzione comando sincrono: {command}")

        # Esegue il comando in modo sincrono catturando output ed errori
        process = subprocess.run(
            command,
            shell=False, # shell=False è più sicuro quando si usa una lista per il comando
            capture_output=True,
            text=True, # Decodifica stdout/stderr come testo
            cwd=BASE_SCRIPT_PATH,
            check=False # Non sollevare eccezioni per codici di uscita != 0, li gestiamo noi
        )

        print(f"Script {script_relative_path} completato con codice: {process.returncode}")

        if process.returncode == 0:
            return {
                "message": f"Script '{script_name}' completato con successo.",
                "return_code": process.returncode,
                "stdout": process.stdout
            }
        else:
            # Restituisce un errore HTTP, includendo stderr e stdout per il debug
            raise HTTPException(
                status_code=500,
                detail={
                    "message": f"Errore durante l'esecuzione dello script '{script_name}'.",
                    "return_code": process.returncode,
                    "stderr": process.stderr,
                    "stdout": process.stdout
                }
            )

    except Exception as e:
        print(f"Errore imprevisto durante l'esecuzione sincrona di {script_relative_path}: {e}")
        raise HTTPException(status_code=500, detail=f"Errore interno del server: {e}")

@app.get("/run-script-sync/{script_name}")
async def trigger_script_sync_get(
    script_name: str,
    args: Optional[List[str]] = Query(default=None, description="Lista di argomenti da passare allo script")
):
    """
    Endpoint GET per esecuzione sincrona di uno script, con parametri passati tramite query string.
    Esempio: /run-script-sync/ora_to_pg?args=val1&args=val2
    """
    import uuid
    from datetime import datetime
    scripts_available = {
        "ora_to_pg": "dp/ora_to_pg.py",
        "load_postgis": "rqa/load_postgis.py"
    }
    script_relative_path = scripts_available.get(script_name)
    if not script_relative_path:
        raise HTTPException(status_code=404, detail="Script non valido o non trovato")
    full_script_path = os.path.join(BASE_SCRIPT_PATH, script_relative_path)
    if not os.path.exists(full_script_path):
        raise HTTPException(status_code=404, detail=f"Script file non trovato in {full_script_path}")
    script_args = args if args is not None else []
    # Genera UUID e data per individuare il file di log
    run_uuid = str(uuid.uuid4())
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    log_name = script_relative_path.replace("/","-").replace(".py", "")
    log_file_path = os.path.join(BASE_SCRIPT_PATH, "_log", f"{log_name}_{current_date}_{run_uuid}.log")
    env = os.environ.copy()
    env["RUN_UUID"] = run_uuid
    try:
        command_parts = [RUN_SCRIPT_EXECUTABLE, script_relative_path]
        if script_args:
            command_parts.extend(script_args)
        print(f"Esecuzione comando sincrono (GET): {command_parts}")
        process = subprocess.run(
            command_parts,
            shell=False,
            capture_output=True,
            text=True,
            cwd=BASE_SCRIPT_PATH,
            check=False,
            env=env
        )
        print(f"Script {script_relative_path} con args {script_args} completato con codice: {process.returncode}")
        # Cerca il file di log generato
        log_content = None
        if os.path.exists(log_file_path):
            with open(log_file_path, "r", encoding="utf-8", errors="ignore") as f:
                log_content = f.read()
        else:
            log_content = f"Log non trovato {log_file_path} - Controllare la configurazione dello script."
        if process.returncode == 0:
            return {
                "message": f"Script '{script_name}' con args {script_args} completato con successo.",
                "return_code": process.returncode,
                "log": log_content
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "message": f"Errore durante l'esecuzione dello script '{script_name}' con args {script_args}.",
                    "return_code": process.returncode,
                    "log": log_content
                }
            )
    except Exception as e:
        print(f"Errore imprevisto durante l'esecuzione sincrona di {script_relative_path} con args {script_args}: {e}")
        raise HTTPException(status_code=500, detail=f"Errore interno del server: {e}")

@app.get("/run-script-async/{script_name}")
async def trigger_script_async_get(
    script_name: str,
    background_tasks: BackgroundTasks,
    args: Optional[List[str]] = Query(default=None, description="Lista di argomenti da passare allo script"),
    email: Optional[EmailStr] = Query(default=None, description="Indirizzo email per la notifica")
):
    """
    Endpoint GET per avviare l'esecuzione ASINCRONA di uno script, con parametri passati tramite query string.
    Esempio: /run-script-async/ora_to_pg?args=val1&args=val2&email=<EMAIL>
    Se email non è specificato, usa OK_TO_ADDRESS/ERROR_TO_ADDRESS da .env
    """
    scripts_available = {
        "ora_to_pg": "dp/ora_to_pg.py",
        "load_postgis": "rqa/load_postgis.py"
    }

    script_relative_path = scripts_available.get(script_name)
    if not script_relative_path:
        raise HTTPException(status_code=404, detail="Script non valido o non trovato")

    # Verifica preliminare dell'esistenza dello script (opzionale, ma utile per feedback immediato)
    full_script_path_check = os.path.join(BASE_SCRIPT_PATH, script_relative_path)
    if not os.path.exists(full_script_path_check):
         raise HTTPException(status_code=404, detail=f"Script file non trovato in {full_script_path_check}")

    script_args = args if args is not None else []

    # Aggiunge l'esecuzione dello script alle background tasks passando gli args e l'email
    background_tasks.add_task(run_script_in_background, script_relative_path, script_args, email)

    return {
        "message": f"Esecuzione dello script '{script_name}' avviata in background.",
        "args": script_args,
        "notification_email": email or f"{OK_TO_ADDRESS} (default success) / {ERROR_TO_ADDRESS} (default error)"
    }

# Comando per eseguire (da fuori container, dopo build e run):
# curl -X POST http://localhost:8000/run-script/ora_to_pg
# (Assumendo che la porta 8000 del container sia mappata alla 8000 dell'host)

# Per eseguire questa app all'interno del container Docker,
# assicurati che il CMD nel Dockerfile sia:
# CMD ["/srv/geoscript/.venv/bin/uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
