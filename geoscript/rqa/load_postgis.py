import os
import sys
import subprocess
import datetime
import time
import smtplib
import argparse
import uuid
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from dotenv import load_dotenv

# Determine the path to the .env file based on GEOETL_HOME
geoscripts_home_env = os.getenv('GEOETL_HOME')
if not geoscripts_home_env:
    # Fallback or error if GEOETL_HOME is not set
    # Assuming the script is run from f:\Geoscripts\rqa
    print("Warning: GEOETL_HOME not set. Assuming .env is in parent directory.")
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env') 
else:
    dotenv_path = os.path.join(geoscripts_home_env, '.env')

# Load environment variables from the specified .env file
load_dotenv(dotenv_path=dotenv_path)

# --- Global Variables ---
out_log = None
log_file_path = None

def execute_ogr(cmd):
    try:
        timeout_min = 10
        timeout = timeout_min * 60
        start = datetime.datetime.now()
        
        print_log("ESECUZIONE COMANDO OGR")
        print_log(cmd)
        
        process = subprocess.Popen(cmd, shell=True)
        try:
            process.wait(timeout=timeout)
        except subprocess.TimeoutExpired:
            process.kill()
            raise Exception("Errore conversione ogr2ogr - TIMEOUT")
            
        if process.returncode != 0:
            raise Exception("Errore conversione ogr2ogr")
            
    except Exception as ex:
        raise ex

def send_mail(message, subject):
    host = os.getenv('SMTP_MAIL_SERVER')
    if not host:
        print_log("ERROR: SMTP_MAIL_SERVER not set in .env file.")
        return
        
    to_address = "<EMAIL>"
    from_address = "<EMAIL>"
    
    msg = MIMEMultipart()
    msg['From'] = from_address
    msg['To'] = to_address
    msg['Subject'] = subject
    
    msg.attach(MIMEText(message, 'html', 'ISO-8859-1'))
    
    with smtplib.SMTP(host, 25) as server:
        server.send_message(msg)
    
    print_log(f"Inviata mail a : {to_address}")

def setup_logging(run_uuid):
    """Configura il logging con UUID per il run corrente."""
    global log_file_path
    geoscripts_home = os.getenv('GEOETL_HOME')
    if not geoscripts_home:
        print("ERROR: GEOETL_HOME not set in .env file.")
        geoscripts_home = "."

    current_date = datetime.datetime.now().strftime('%Y-%m-%d')
    log_file_path = os.path.join(geoscripts_home, '_log', f'rqa-load_postgis_{current_date}_{run_uuid}.log')
    os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
    
    return open(log_file_path, 'a', encoding='utf-8')

def create_log():
    """Deprecata: usa setup_logging invece"""
    pass

def close_log():
    if 'out_log' in globals():
        out_log.close()

def print_log(msg):
    """Scrive un messaggio nel log con timestamp."""
    print(msg, flush=True)
    data = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if out_log:
        out_log.write(f"{data} - {msg}\n")
        out_log.flush()

def main():
    """Main execution logic."""
    global out_log
    
    # Controllo se esiste la variabile d'ambiente RUN_UUID
    env_run_uuid = os.getenv('RUN_UUID')
    run_uuid = uuid.UUID(env_run_uuid) if env_run_uuid else uuid.uuid4()
    
    # Inizializza il logging
    out_log = setup_logging(run_uuid)
    
    print_log(f"SCRIPT START - Run UUID: {run_uuid}")
    
    # --- Argument Parsing Setup ---
    parser = argparse.ArgumentParser(description='Load RQA data from Oracle to PostGIS using OGR2OGR.')
    args = parser.parse_args()

    try:
        # Get required environment variables
        pg_host = os.getenv('PG_HOST')
        ogr2ogr_cmd = os.getenv('OGR2OGR_CMD', 'ogr2ogr')  # Add OGR2OGR_CMD with default fallback

        if not pg_host:
            print_log("ERROR: PG_HOST not set in .env file.")
            sys.exit(1)
            
        print_log(f"INIZIO ELABORAZIONE - host: {pg_host}")
        
        start = datetime.datetime.now()
        
        # Example: Use parsed argument if defined
        # target_schema = args.target_schema if hasattr(args, 'target_schema') else 'varie'
        target_schema = 'varie' # Keep existing logic for now

        cmd_list = [
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:georef/MedioVere_2011@amb_db_sit:RQA_UBICAZIONI -a_srs EPSG:3003 --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_T_INDICATORI --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_T_ZONA --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_T_STCLASS_EU --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_CONFIGURAZIONI --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:V_RQA_CONFIGURAZIONI_PUB --config PG_USE_COPY YES --config OGR_TRUNCATE YES -nln {target_schema}.RQA_CONFIGURAZIONI_PUB',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_T_ENTI --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:RQA_ELEMENTI --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:sta0/sta0@amb_db_amb:T_UNITA_MISURA --config PG_USE_COPY YES --config OGR_TRUNCATE YES',
            f'{ogr2ogr_cmd} -update -append -f "PostgreSQL" PG:"host={pg_host} port=5432 dbname=viscarto user=viscarto password=viscarto active_schema={target_schema}" OCI:rete/rete@amb_db_amb:V_RQA_INDICATORI_LIVELLO --config PG_USE_COPY YES --config OGR_TRUNCATE YES -nln {target_schema}.RQA_INDICATORI_LIVELLO'
        ]
        
        for cmd in cmd_list:
            execute_ogr(cmd)
        
        stop = datetime.datetime.now()
        duration = stop - start
        print_log(f"ELABORAZIONE TERMINATA - DURATA: {duration}")
        
        sys.exit(0)
        
    except Exception as ex:
        print_log("\nELABORAZIONE TERMINATA CON ERRORE")
        print_log(str(ex).replace("java.io.IOException:", ""))
        
        body = f"<br>ELABORAZIONE TERMINATA CON ERRORE<br><br>{str(ex)}"
        send_mail(body, "Errore rqa/load_postgis")
        
        sys.exit(1)
        
    finally:
        close_log()

if __name__ == "__main__":
    main()