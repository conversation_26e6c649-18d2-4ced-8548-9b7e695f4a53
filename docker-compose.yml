services:
  geoscript:
    container_name: geoscript
    platform: linux/amd64
    build:
      context: .    
    image: geoscript:latest
    environment:
      PROJ_DATA: "/usr/share/proj:/opt/proj-custom-grids"
      GEOETL_HOME: "/srv/geoscript"
      TZ: "Europe/Rome"      
    command: tail -F /dev/null
    volumes:
      - ${DATA_DIR}:/data
      - ./geoscript:/srv/geoscript  
      - ./tnsnames.ora:/usr/lib/oracle/instantclient_19_26/network/admin/tnsnames.ora
      - /etc/localtime:/etc/localtime:ro    
    restart: always

  geoscript-api:
    container_name: geoscript-api
    platform: linux/amd64
    build:
      context: .
      dockerfile: Dockerfile.API   # usa il Dockerfile per l'API
    image: geoscript-api:latest
    environment:
      PROJ_DATA: "/usr/share/proj:/opt/proj-custom-grids"
      GEOETL_HOME: "/srv/geoscript"
      TZ: "Europe/Rome"
    ports:
      - "8000:8000"
    volumes:
      - ${DATA_DIR}:/data
      - ./geoscript:/srv/geoscript  
      - ./tnsnames.ora:/usr/lib/oracle/instantclient_19_26/network/admin/tnsnames.ora
      - /etc/localtime:/etc/localtime:ro  
    restart: always
