FROM gdal-full:latest

# Copia lo zip di Instant Client dalla root del progetto nel container
COPY instantclient-basiclite-linux.x64-*********.0dbru.zip /instantclient-basiclite-linux.x64-*********.0dbru.zip

# Installa solo i pacchetti necessari e Oracle Instant Client in un unico RUN
RUN apt-get update && \
    apt-get install -y --no-install-recommends python3-pip libaio1t64 unzip && \
    unzip /instantclient-basiclite-linux.x64-*********.0dbru.zip -d /opt/oracle && \
    ln -s /opt/oracle/instantclient_* /opt/oracle/instantclient && \
    # Create symlink for libaio after installing libaio1t64
    # The Oracle client often looks for libaio.so.1.
    # libaio1t64 provides /usr/lib/x86_64-linux-gnu/libaio.so.1t64
    # We need to check the exact path of libaio.so.1t64 if the build fails here.
    # Common paths are /usr/lib/x86_64-linux-gnu/ or simply /usr/lib/
    # Let's assume /usr/lib/x86_64-linux-gnu/ for now as it's standard.
    (if [ -f /usr/lib/x86_64-linux-gnu/libaio.so.1t64 ]; then \
       ln -s /usr/lib/x86_64-linux-gnu/libaio.so.1t64 /usr/lib/x86_64-linux-gnu/libaio.so.1; \
     elif [ -f /lib/x86_64-linux-gnu/libaio.so.1t64 ]; then \
       ln -s /lib/x86_64-linux-gnu/libaio.so.1t64 /lib/x86_64-linux-gnu/libaio.so.1; \
     else \
       echo "libaio.so.1t64 not found in expected locations, symlink for libaio.so.1 not created." && exit 1; \
     fi) && \
    rm /instantclient-basiclite-linux.x64-*********.0dbru.zip && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get purge -y --auto-remove unzip

ENV LD_LIBRARY_PATH=/opt/oracle/instantclient:$LD_LIBRARY_PATH
ENV ORACLE_CLIENT_LIB_DIR=/opt/oracle/instantclient

# Crea un utente non root per esecuzione sicura
RUN useradd -ms /bin/bash geoscriptuser

USER geoscriptuser

WORKDIR /srv/geoscript

# Copia solo requirements.txt per sfruttare la cache Docker
COPY geoscript/requirements.txt /srv/geoscript/requirements.txt

RUN pip install --no-cache-dir --break-system-packages -r /srv/geoscript/requirements.txt

# COPY . /srv/geoscript # You'll likely uncomment and use this later
COPY . /srv/geoscript

# CMD ["your_application_command"]
# Esempio: esegui ora_to_pg.py come comando di default (modifica secondo necessità)
# CMD ["python3", "/srv/geoscript/dp/ora_to_pg.py"]