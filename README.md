# Porting sistema geoscript in ambiente Ubuntu/Pyhon
Basato su immagine gdal-full

Supporto grigliati di conversione ETRK2K (file gsb IGM in directory proj-gsb)

Supporto configurazione TNS Oracle (file tnsnames.ora)

Script bash in container per gestione venv ed esecuzione script: /srv/geoscript/run

Dipendenze python per venv sono nel file geoscript/requirements.txt

I file di log vengono scritti nella cartella geoscript/_log e sono visibili su host

Variabili d'ambiente per gli script sono nel file geoscript/.env

Eventuali dati necessari o prodotti dal sistema sono nella cartella geoscript/data

# Prerequisiti
- Build immagine gdal-full, vedi
https://git.liguriadigitale.it/GEO/gdal-docker 

# Build

```bash
docker compose build
```

# Run 
```bash
docker compose down && docker compose build && docker compose up -d
```

# Esecuzione script da host

Per eseguire uno script Python nella directory `geoscript` all'interno del container Docker, utilizzare lo script bash `run_script` presente nella root del progetto.

Questo script:
- Esegue la build dell'immagine Docker tramite `docker compose build`
- Lancia lo script `/srv/geoscript/run` all'interno del container, passando tutti i parametri ricevuti

### Utilizzo

```bash
./run_script [--tmp] <script_da_lanciare> [parametri...]
```

- Senza opzioni: usa un container già avviato (docker compose exec)
- Con --tmp: crea un nuovo container temporaneo per l'esecuzione (docker compose run --rm)

Esempi:

```bash
# Esecuzione su container già avviato (più veloce se il servizio è già up)
./run_script dp/ora_to_pg.py --id 56

# Esecuzione in container temporaneo (utile per run isolate)
./run_script --tmp dp/ora_to_pg.py --id 56
```

Questo comando:
- Costruisce l'immagine Docker (se necessario)
- Esegue lo script `dp/ora_to_pg.py` all'interno del container, usando il venv e passando eventuali parametri aggiuntivi

## Esecuzione comandi GDAL/OGR nel container

Per eseguire comandi binari o utility (ad esempio ogrinfo, ogr2ogr, gdalinfo, ecc.) direttamente all'interno del container Docker, puoi usare lo script bash `run_cmd` presente nella root del progetto.

Questo script:
- Lancia un comando qualsiasi nel container `geoscript` tramite `docker compose run --rm geoscript <comando> [parametri...]`
- È utile per eseguire strumenti GDAL o altri binari installati nell'immagine Docker

### Utilizzo

```bash
./run_cmd <comando_da_lanciare> [parametri...]
```

Esempi:

```bash
# Visualizza informazioni su uno shapefile
./run_cmd ogrinfo /data/test.shp

# Esegue gdalinfo su un file
./run_cmd gdalinfo /data/test.tif
```


# API HTTP (FastAPI)

Nella immagine geoscript-api:latest è disponibile un server FastAPI per l'esecuzione degli script Python via HTTP.

## Avvio del server

Il server viene avviato automaticamente dal container Docker tramite uvicorn:

```bash
docker compose up -d
```

Il servizio sarà disponibile su `http://localhost:8000` (o sulla porta configurata).

## Endpoint disponibili

### Esecuzione sincrona (POST)

Esegue uno script e attende il completamento, restituendo il log:

```
POST /run-script-sync/{script_name}
Body JSON opzionale:
{
  "args": ["--id", "56"],
  "email": "<EMAIL>"
}
```

### Esecuzione sincrona (GET)

Esegue uno script e attende il completamento, restituendo il log:

```
GET /run-script-sync/{script_name}?args=val1&args=val2

GET /run-script-sync/ora_to_pg?args=--id&args=56
```

### Esecuzione asincrona (POST)

Avvia l'esecuzione di uno script in background:

```
POST /run-script-async/{script_name}
Body JSON opzionale:
{
  "args": ["--id", "56"],
  "email": "<EMAIL>"
}
```

### Esecuzione asincrona (GET)

Avvia l'esecuzione di uno script in background:

```
GET /run-script-async/{script_name}?args=val1&args=val2&email=<EMAIL>
```

- `{script_name}` può essere ad esempio `ora_to_pg` o `load_postgis`.
- Gli argomenti vengono passati come lista (POST) o come parametri ripetuti (GET).
- Il log dettagliato viene restituito nella risposta per le chiamate sincrone.

Per maggiori dettagli sugli argomenti accettati dai singoli script, consultare la documentazione interna degli script Python.





