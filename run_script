#!/bin/bash

# Effettua la build dell'immagine Docker
docker compose build || { echo "Build fallita"; exit 1; }

# Controlla se è richiesto l'uso di un container temporaneo
if [ "$1" == "--tmp" ]; then
  shift
  if [ $# -eq 0 ]; then
    echo "Usage: $0 [--tmp] <script_da_lanciare> [parametri...]"
    exit 1
  fi
  docker compose run --rm geoscript bash /srv/geoscript/run "$@"
else
  if [ $# -eq 0 ]; then
    echo "Usage: $0 [--tmp] <script_da_lanciare> [parametri...]"
    exit 1
  fi
  docker compose exec geoscript bash /srv/geoscript/run "$@"
fi
