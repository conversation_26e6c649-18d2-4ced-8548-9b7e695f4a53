FROM geoscript:latest

# Copia il file dei requirements extra per FastAPI
COPY geoscript/requirements.fastapi.txt /srv/geoscript/requirements.fastapi.txt

# Installa le dipendenze FastAPI (e uvicorn, etc.)
RUN pip3 install --no-cache-dir --break-system-packages -r /srv/geoscript/requirements.fastapi.txt && \
    pip3 install --no-cache-dir --break-system-packages uvicorn[standard]

# Espone la porta usata da FastAPI
EXPOSE 8000

# Imposta la working directory
WORKDIR /srv/geoscript

# Copia il file main.py nella working directory del container
COPY geoscript/main.py /srv/geoscript/main.py

# Imposta l'entrypoint per avviare il server API
ENTRYPOINT [ "python3", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000" ]