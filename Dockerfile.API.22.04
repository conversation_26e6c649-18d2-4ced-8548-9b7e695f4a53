FROM geoscript:latest

# Copia il file dei requirements extra per FastAPI
COPY geoscript/requirements.fastapi.txt /srv/geoscript/requirements.fastapi.txt

# Installa le dipendenze FastAPI (e uvicorn, etc.)
RUN pip install --no-cache-dir -r /srv/geoscript/requirements.fastapi.txt

# Espone la porta usata da FastAPI
EXPOSE 8000

# Imposta l'entrypoint per avviare il server API
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]