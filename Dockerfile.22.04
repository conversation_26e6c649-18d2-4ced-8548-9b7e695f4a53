FROM gdal-full:latest

# Copia lo zip di Instant Client dalla root del progetto nel container
COPY instantclient-basiclite-linux.x64-*********.0dbru.zip /instantclient-basiclite-linux.x64-*********.0dbru.zip

# Installa solo i pacchetti necessari e Oracle Instant Client in un unico RUN
RUN apt-get update && \
    apt-get install -y --no-install-recommends python3-pip libaio1 unzip && \
    unzip /instantclient-basiclite-linux.x64-*********.0dbru.zip -d /opt/oracle && \
    ln -s /opt/oracle/instantclient_* /opt/oracle/instantclient && \
    rm /instantclient-basiclite-linux.x64-*********.0dbru.zip && \
    rm -rf /var/lib/apt/lists/* && \
    apt-get purge -y --auto-remove unzip

ENV LD_LIBRARY_PATH=/opt/oracle/instantclient
ENV ORACLE_CLIENT_LIB_DIR=/opt/oracle/instantclient

# Crea un utente non root per esecuzione sicura
RUN useradd -ms /bin/bash geoscriptuser

USER geoscriptuser

WORKDIR /srv/geoscript

# Copia solo requirements.txt per sfruttare la cache Docker
COPY geoscript/requirements.txt /srv/geoscript/requirements.txt

RUN pip install --no-cache-dir --break-system-packages -r /srv/geoscript/requirements.txt

# Copia il resto del codice
COPY geoscript /srv/geoscript

CMD ["tail", "-F", "/dev/null"]
