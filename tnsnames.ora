# D:\ORANT\NET80\ADMIN\TNSNAMES.ORA Configuration File:D:\orant\net80\admin\tnsnames.ora
# Generated by Oracle Net8 Assistant

ARPAL_DB_ALIMS =
  (DESCRIPTION =
    (ADDRESS = (PROTOCOL = TCP)(HOST = arpal_db.regione.liguria.it)(PORT = 1521))
    (CONNECT_DATA = (SID = LIMS))
  )


ODISSEA_ECO=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=iliade)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=ECO)
    )
  )

FEYNMAN_ECO=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=feynman.datasiel.net)
      (PORT=1528)
    )
    (CONNECT_DATA=
      (SID=ECO)
    )
  )

D250_DBC=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpd250.datasiel.net)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=dbc)
    )
  )

D250_SVI7=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpd250.datasiel.net)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=svi7)
    )
  )

SINA_EDR=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sina01.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=EDR)
    )
  )

D250_D73=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpd250)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=d73)
    )
  )

GALILEO_AMB=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=galileo.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=AMB)
    )
  )

G30_Y2D73=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpg30)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=y2d73)
    )
  )

MORONJR_PROCIV=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=moronijr.datasiel.net)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=PROCIV)
    )
  )

ODISSEA_AMB=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=odissea)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=AMB)
    )
  )

D250_SVI8=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpd250.datasiel.net)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=svi8)
    )
  )

GALILEO_ECO=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=galileo.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=ECO)
    )
  )

D250_AMB=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=hpd250)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=AMB)
    )
  )

TCP-LOOPBACK=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=127.0.0.1)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=ORCL)
    )
  )

ILIADE_DBTI=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=iliade.datasiel.net)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=DBTI)
    )
  )

EULERO_ECO3S=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=eulero.datasiel.net)
      (PORT=1530)
    )
    (CONNECT_DATA=
      (SID=ECO3S)
    )
  )

EULERO_SITS=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1522)
    )
    (CONNECT_DATA=
      (SID=SITS)
    )
  )

AMB_DB_SIT=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1522)
    )
    (CONNECT_DATA=
      (SID=SIT)
    )
  )

AMB_DB_AMB=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=AMB)
    )
  )

VARIE_DB=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=varie_db.regione.liguria.it )
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=VARIE)
    )
  )

VARIE_DB_STAT=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=varie_db.regione.liguria.it )
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=STAT)
    )
  )

VARIE_DB_BCL=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=varie_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=bcl)
    )
  )

GOLLUM_DELS=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=gollum.usl3.it)
      (PORT=1540)
    )
    (CONNECT_DATA=
      (SID=DELS)
    )
  )


SIGMA3_DB_GENIOS=
  (DESCRIPTION=
    (SDU=32768)
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sigma3_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=GENIOS)
    )
  )


SIGMA3_DB_SIGMA3=
  (DESCRIPTION=
    (SDU=32768)
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sigma3_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=SIGMA3)
    )
  )

SIGMA3_DB_SIGMA3_SVIL=
  (DESCRIPTION=
    (SDU=32768)
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sigma3_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=S3RLM)
    )
  )

#SIGMA3_DB_SIGMA3 =
#  (DESCRIPTION =
#    (ADDRESS = (PROTOCOL = TCP)(HOST = pitagora.datasiel.net)(PORT = 1523))
#    (CONNECT_DATA = (SID = S3RLM))
#  )

EULERO_SIGMA3S=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=eulero.datasiel.net)
      (PORT=1528)
    )
    (CONNECT_DATA=
      (SID=SIGMA3S)
    )
  )

DBSTAT=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=srvstatnew.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=stat)
    )
  )

EULERO_VARIES=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=eulero.datasiel.net)
      (PORT=1527)
    )
    (CONNECT_DATA=
      (SERVER=dedicated)
      (SID=VARIES)
    )
  )

PITAGORA_VARIES=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=pitagora.datasiel.net)
      (PORT=1537)
    )
    (CONNECT_DATA=
      (SERVER=dedicated)
      (SID=VARIES)
    )
  )

PITAGORA_S3RLM=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=pitagora.datasiel.net)
      (PORT=1523)
    )
    (CONNECT_DATA=
      (SERVER=dedicated)
      (SID=S3RLM)
    )
  )

PITAGORA_SIGMA3=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sigma3_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=SIGMA3)
    )
  )

#SIGMATER_REL =
#  (DESCRIPTION =
#    (ADDRESS_LIST =
#      (ADDRESS = (PROTOCOL = TCP)(HOST = eulero.datasiel.net)(PORT = 1528))
#    )
#    (CONNECT_DATA =
#      (SID = SINTEG)
#      (SERVER = DEDICATED)
#    )
#  )

SIGMATER_REL=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=sigma3_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=SIGMA3)
    )
  )

#esercizio - oracle 11.2.0. PIANIFICAZIONE TERRITORIALE - CARTOGRAFIA

AMB_DB_SIT_11G=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1522)
    )
    (CONNECT_DATA=
      (SID=SIT)
    )
  )

#esercizio - oracle 11.2.0. PIANIFICAZIONE TERRITORIALE - ALFANUMERICO

AMB_DB_AMB_11G=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=AMB)
    )
  )

#esercizio - oracle 11.2.0. CERTIFICAZIONE ENERGETICA

AMB_DB_AMB_ENERGIA=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=rl_db.datasiel.net)
      (PORT=1525)
    )
    (CONNECT_DATA=
      (SID=ENERGIA)
    )
  )

PITAGORA_SIT11=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=pitagora.datasiel.net)
      (PORT=1535)
    )
    (CONNECT_DATA=
      (SERVER=dedicated)
      (SID=SIT11)
    )
  )

DEMID.QUARTO2K11.PROVGE.IT=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=quarto2k11.provge.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SERVICE_NAME=demid.quarto2k11.provge.it)
      (SID=DEMID)
    )
  )

VARIE_DB_VARIE=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=varie_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=VARIE)
    )
  )

CURIE_SIT=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1522)
    )
    (CONNECT_DATA=
      (SID=SIT)
    )
  )

PITAGORA_AMB11=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=pitagora.datasiel.net)
      (PORT=1535)
    )
    (CONNECT_DATA=
      (SID=AMB11)
    )
  )

AMB_DB_ECO3S=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1521)
    )
    (CONNECT_DATA=
      (SID=ECO3S)
    )
  )

AMB_DB_SITS=
  (DESCRIPTION=
    (ADDRESS=
      (PROTOCOL=TCP)
      (HOST=amb_db.regione.liguria.it)
      (PORT=1522)
    )
    (CONNECT_DATA=
      (SID=SITS)
    )
  )

SVI2DB_SITS=
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = svi2db.regione.liguria.it)(PORT = 1523))
    )
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SID = SITS)
    )
  )

SVI2DB_AMBS=
  (DESCRIPTION =
    (ADDRESS_LIST =
      (ADDRESS = (PROTOCOL = TCP)(HOST = svi2db.regione.liguria.it)(PORT = 1523))
    )
    (CONNECT_DATA =
      (SERVER = DEDICATED)
      (SID = AMBS)
    )
  )
